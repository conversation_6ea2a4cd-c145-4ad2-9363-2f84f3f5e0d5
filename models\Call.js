const mongoose = require('mongoose');

// نموذج المكالمات
const callSchema = new mongoose.Schema({
    callId: {
        type: String,
        required: true,
        unique: true
    },
    callerId: {
        type: String,
        required: true
    },
    callerCharacterId: {
        type: String,
        required: true
    },
    receiverId: {
        type: String,
        required: true
    },
    receiverCharacterId: {
        type: String,
        required: true
    },
    callerName: {
        type: String,
        required: true
    },
    receiverName: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['calling', 'answered', 'declined', 'missed', 'ended'],
        default: 'calling'
    },
    duration: {
        type: Number,
        default: 0 // بالثواني
    },
    startTime: {
        type: Date,
        default: Date.now
    },
    endTime: {
        type: Date,
        default: null
    }
});

// إنشاء فهارس للبحث السريع
callSchema.index({ callerId: 1, callerCharacterId: 1 });
callSchema.index({ receiverId: 1, receiverCharacterId: 1 });

module.exports = mongoose.model('Call', callSchema);
