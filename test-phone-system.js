// اختبار نظام الجوالات المحدث
const mongoose = require('mongoose');
const Phone = require('./models/Phone');
const Contact = require('./models/Contact');
const TwitterAccount = require('./models/TwitterAccount');
const ActiveCharacter = require('./models/activeCh');

async function testPhoneSystem() {
    try {
        console.log('🧪 بدء اختبار نظام الجوالات المحدث...\n');

        // بيانات اختبار
        const testUserId = 'test_user_123';
        const character1Id = 'character_1';
        const character2Id = 'character_2';

        // تنظيف البيانات السابقة
        await Phone.deleteMany({ userId: testUserId });
        await Contact.deleteMany({ ownerId: testUserId });
        await TwitterAccount.deleteMany({ userId: testUserId });
        await ActiveCharacter.deleteMany({ userId: testUserId });

        console.log('✅ تم تنظيف البيانات السابقة');

        // إنشاء شخصية نشطة
        const activeChar1 = new ActiveCharacter({
            userId: testUserId,
            characterId: character1Id,
            characterNum: 'ch1'
        });
        await activeChar1.save();
        console.log('✅ تم إنشاء الشخصية النشطة الأولى');

        // إنشاء جوال للشخصية الأولى
        const phone1 = new Phone({
            userId: testUserId,
            characterId: character1Id,
            phoneNumber: '+************',
            originalOwner: 'شخصية 1',
            currentHolder: 'شخصية 1'
        });
        await phone1.save();
        console.log('✅ تم إنشاء جوال للشخصية الأولى:', phone1.phoneNumber);

        // إنشاء جوال للشخصية الثانية
        const phone2 = new Phone({
            userId: testUserId,
            characterId: character2Id,
            phoneNumber: '+************',
            originalOwner: 'شخصية 2',
            currentHolder: 'شخصية 2'
        });
        await phone2.save();
        console.log('✅ تم إنشاء جوال للشخصية الثانية:', phone2.phoneNumber);

        // إنشاء حساب تويتر للشخصية الأولى
        const twitter1 = new TwitterAccount({
            userId: testUserId,
            characterId: character1Id,
            username: 'character1_twitter',
            displayName: 'الشخصية الأولى'
        });
        await twitter1.save();
        console.log('✅ تم إنشاء حساب تويتر للشخصية الأولى');

        // إنشاء حساب تويتر للشخصية الثانية
        const twitter2 = new TwitterAccount({
            userId: testUserId,
            characterId: character2Id,
            username: 'character2_twitter',
            displayName: 'الشخصية الثانية'
        });
        await twitter2.save();
        console.log('✅ تم إنشاء حساب تويتر للشخصية الثانية');

        // اختبار البحث عن الجوالات
        const foundPhone1 = await Phone.findOne({ userId: testUserId, characterId: character1Id });
        const foundPhone2 = await Phone.findOne({ userId: testUserId, characterId: character2Id });

        console.log('\n📱 اختبار البحث عن الجوالات:');
        console.log('الشخصية الأولى:', foundPhone1 ? foundPhone1.phoneNumber : 'غير موجود');
        console.log('الشخصية الثانية:', foundPhone2 ? foundPhone2.phoneNumber : 'غير موجود');

        // اختبار إنشاء جهة اتصال
        const contact = new Contact({
            ownerId: testUserId,
            ownerCharacterId: character1Id,
            contactUserId: 'other_user_456',
            contactCharacterId: 'other_character_1',
            contactName: 'صديق',
            contactPhone: '+966509876543'
        });
        await contact.save();
        console.log('✅ تم إنشاء جهة اتصال للشخصية الأولى');

        // اختبار البحث عن جهات الاتصال
        const contacts1 = await Contact.find({ ownerId: testUserId, ownerCharacterId: character1Id });
        const contacts2 = await Contact.find({ ownerId: testUserId, ownerCharacterId: character2Id });

        console.log('\n📞 اختبار جهات الاتصال:');
        console.log('جهات اتصال الشخصية الأولى:', contacts1.length);
        console.log('جهات اتصال الشخصية الثانية:', contacts2.length);

        // اختبار تغيير الشخصية النشطة
        activeChar1.characterId = character2Id;
        await activeChar1.save();
        console.log('✅ تم تغيير الشخصية النشطة إلى الثانية');

        // اختبار البحث بناءً على الشخصية النشطة
        const activeCharacter = await ActiveCharacter.findOne({ userId: testUserId });
        const activePhone = await Phone.findOne({ 
            userId: testUserId, 
            characterId: activeCharacter.characterId 
        });

        console.log('\n🔄 اختبار الشخصية النشطة:');
        console.log('الشخصية النشطة:', activeCharacter.characterId);
        console.log('جوال الشخصية النشطة:', activePhone ? activePhone.phoneNumber : 'غير موجود');

        console.log('\n🎉 تم اجتياز جميع الاختبارات بنجاح!');
        console.log('\n📋 ملخص النتائج:');
        console.log('- كل شخصية لها جوال منفصل ✅');
        console.log('- كل شخصية لها حساب تويتر منفصل ✅');
        console.log('- جهات الاتصال منفصلة لكل شخصية ✅');
        console.log('- النظام يتعامل مع الشخصية النشطة بشكل صحيح ✅');

    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    // تحتاج لتوصيل قاعدة البيانات أولاً
    console.log('⚠️  لتشغيل الاختبار، تحتاج لتوصيل قاعدة البيانات أولاً');
    console.log('مثال:');
    console.log('mongoose.connect(process.env.MONGODB_URI).then(() => testPhoneSystem());');
}

module.exports = { testPhoneSystem };
