const mongoose = require('mongoose');

// نموذج الجوال المتطور
const phoneSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true
    },
    characterId: {
        type: String,
        required: true // رقم هوية الشخصية
    },
    phoneNumber: {
        type: String,
        required: true
        // إزالة unique لأن كل شخصية ستحتاج رقم منفصل
    },
    // نظام الحماية
    passcode: {
        type: String,
        default: null // الرقم السري (4 أرقام)
    },
    isLocked: {
        type: Boolean,
        default: false
    },
    failedAttempts: {
        type: Number,
        default: 0
    },
    lockedUntil: {
        type: Date,
        default: null
    },
    // معلومات الملكية
    originalOwner: {
        type: String,
        required: true // المالك الأصلي
    },
    currentHolder: {
        type: String,
        required: true // الحامل الحالي
    },
    // إعدادات الخلفية
    wallpaper: {
        type: String,
        default: 'default'
    },
    customWallpaperUrl: {
        type: String,
        default: null
    },
    wallpaperType: {
        type: String,
        enum: ['default', 'gradient', 'image'],
        default: 'default'
    },
    // إعدادات إضافية
    theme: {
        type: String,
        enum: ['light', 'dark'],
        default: 'dark'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// إنشاء فهرس مركب للبحث السريع - جوال منفصل لكل شخصية
phoneSchema.index({ userId: 1, characterId: 1 }, { unique: true }); // فهرس فريد لكل شخصية
phoneSchema.index({ phoneNumber: 1 }, { unique: true }); // رقم الجوال فريد عالمياً
phoneSchema.index({ currentHolder: 1 });
phoneSchema.index({ originalOwner: 1 });

// تحديث updatedAt تلقائياً عند التعديل
phoneSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// دالة للتحقق من صحة الرقم السري
phoneSchema.methods.validatePasscode = function(inputPasscode) {
    return this.passcode === inputPasscode;
};

// دالة لقفل الجوال بعد محاولات فاشلة
phoneSchema.methods.lockPhone = function(minutes = 5) {
    this.isLocked = true;
    this.lockedUntil = new Date(Date.now() + minutes * 60 * 1000);
    this.failedAttempts = 0;
};

// دالة للتحقق من انتهاء فترة القفل
phoneSchema.methods.checkLockExpiry = function() {
    if (this.isLocked && this.lockedUntil && new Date() > this.lockedUntil) {
        this.isLocked = false;
        this.lockedUntil = null;
        this.failedAttempts = 0;
        return true;
    }
    return false;
};

module.exports = mongoose.model('Phone', phoneSchema);
