const mongoose = require('mongoose');

// نموذج جهات الاتصال
const contactSchema = new mongoose.Schema({
    ownerId: {
        type: String,
        required: true
    },
    ownerCharacterId: {
        type: String,
        required: true
    },
    contactUserId: {
        type: String,
        required: true
    },
    contactCharacterId: {
        type: String,
        required: true
    },
    contactName: {
        type: String,
        required: true
    },
    contactPhone: {
        type: String,
        required: true
    },
    isFavorite: {
        type: Boolean,
        default: false
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

// فهرس مركب لضمان عدم تكرار جهة الاتصال لنفس المالك والشخصية
contactSchema.index({ ownerId: 1, ownerCharacterId: 1, contactUserId: 1, contactCharacterId: 1 }, { unique: true });

module.exports = mongoose.model('Contact', contactSchema);
