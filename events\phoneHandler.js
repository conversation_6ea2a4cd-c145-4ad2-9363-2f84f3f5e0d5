const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle, AttachmentBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { createCanvas, loadImage } = require('@napi-rs/canvas');
const fs = require('fs');
const path = require('path');
const Phone = require('../models/Phone');
const TwitterAccount = require('../models/TwitterAccount');
const Tweet = require('../models/Tweet');
const Contact = require('../models/Contact');
const WhatsAppMessage = require('../models/WhatsAppMessage');
const Call = require('../models/Call');

module.exports = {
    // معالج فتح الجوال للأعضاء - إنشاء تلقائي للجوال
    async handleMemberPhoneOpen(interaction, client) {
        try {
            const userId = interaction.user.id;
            const username = interaction.user.username;

            console.log(`Member ${userId} opening phone from button`);

            // البحث عن الشخصية النشطة
            const activeCharacter = await require('../models/activeCh').findOne({ userId });
            let characterName = username; // افتراضي
            let characterId = userId; // افتراضي

            if (activeCharacter) {
                const character = await require('../models/Character').findOne({
                    userId,
                    characterName: activeCharacter.characterId
                });
                if (character) {
                    characterName = character.characterName;
                    characterId = activeCharacter.characterId;
                }
            }

            // البحث عن جوال موجود للشخصية المحددة أو إنشاء جديد
            let userPhone = await Phone.findOne({ userId, characterId });
            let isNewPhone = false;

            if (!userPhone) {
                // إنشاء رقم جوال عشوائي جديد
                const phoneNumber = this.generatePhoneNumber();

                userPhone = new Phone({
                    userId,
                    characterId: characterId, // استخدام characterId المحدد
                    phoneNumber,
                    originalOwner: characterName,
                    currentHolder: characterName,
                    wallpaper: 'default',
                    createdAt: new Date()
                });

                await userPhone.save();
                isNewPhone = true;
                console.log(`Created new phone for character ${characterName} (${characterId}): ${phoneNumber}`);
            }

            // التحقق من كلمة السر إذا كانت مفعلة
            if (userPhone.password && !isNewPhone) {
                await this.showPasswordPrompt(interaction, userPhone);
                return;
            }

            // تحميل الصورة الجاهزة وإضافة المعلومات عليها
            const phoneImage = await this.createCustomPhoneImage(userId, userPhone.phoneNumber);
            const attachment = new AttachmentBuilder(phoneImage, { name: 'iphone.png' });

            // إنشاء رسالة ترحيب مناسبة
            const welcomeMessage = isNewPhone ?
                `**Welcome ${characterName}!**\n\nتم إنشاء جوالك الجديد بنجاح!\n**رقم جوالك:** ${userPhone.phoneNumber}\n\nاستمتع بجميع الميزات المتاحة!` :
                `**Welcome ${characterName}!**\n\n**رقم جوالك:** ${userPhone.phoneNumber}`;

            // إنشاء منيو الخيارات
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`phone_menu_${userId}`)
                .setPlaceholder('اختر ما تريد فعله...')
                .addOptions([
                    {
                        label: 'فتح iPhone',
                        description: 'فتح الشاشة الرئيسية للجوال',
                        value: 'open_phone'
                    }
                ]);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            // إنشاء embed مع الرسالة
            const embed = new EmbedBuilder()
                .setTitle('جوالك الذكي')
                .setDescription(welcomeMessage)
                .setColor(isNewPhone ? '#00ff00' : '#1DA1F2')
                .setImage('attachment://iphone.png')
                .setFooter({ text: 'نظام الجوالات الذكي' })
                .setTimestamp();

            await interaction.reply({
                embeds: [embed],
                files: [attachment],
                components: [row],
                ephemeral: true
            });

        } catch (error) {
            console.error('خطأ في فتح جوال العضو:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: 'حدث خطأ أثناء فتح الجوال.', ephemeral: true });
            }
        }
    },

    // عرض نافذة إدخال كلمة السر
    async showPasswordPrompt(interaction, userPhone) {
        const modal = new ModalBuilder()
            .setCustomId(`phone_password_prompt_${userPhone._id}`)
            .setTitle('🔒 كلمة سر الجوال');

        const passwordInput = new TextInputBuilder()
            .setCustomId('phone_password')
            .setLabel('أدخل كلمة السر (4 أرقام)')
            .setStyle(TextInputStyle.Short)
            .setMinLength(4)
            .setMaxLength(4)
            .setPlaceholder('مثال: 1234')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(passwordInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    async handlePhoneInteraction(interaction, client) {
        try {
            const userId = interaction.user.id;
            const customId = interaction.customId;

            console.log(`Phone interaction: ${customId} from user ${userId}`);

            // معالجة المنيو
            if (interaction.isStringSelectMenu()) {
                const selectedValue = interaction.values[0];
                console.log(`Menu selection: ${selectedValue} from menu: ${customId}`);

                if (selectedValue === 'open_phone') {
                    console.log('Opening phone home from menu...');
                    await this.showPhoneHome(interaction, client);
                    return;
                }

                // معالجة منيو تويتر
                if (customId.startsWith('twitter_menu_') ||
                    customId.startsWith('twitter_signup_menu_') ||
                    customId.startsWith('twitter_profile_menu_')) {
                    console.log('Handling Twitter menu...');
                    await this.handleTwitterMenuSelection(interaction, client, selectedValue);
                    return;
                }

                // توجيه باقي الخيارات
                console.log('Handling general menu selection...');
                await this.handleMenuSelection(interaction, client, selectedValue);
                return;
            }

            // فتح الجوال الرئيسي (للأزرار القديمة)
            if (customId.startsWith('open_phone_') || customId.startsWith('unlock_iphone_')) {
                console.log('Opening phone home...');
                await this.showPhoneHome(interaction, client);
                return;
            }

            // التنقل في الجوال
            if (customId === 'phone_home') {
                await this.showPhoneHome(interaction, client);
                return;
            }

            // تطبيق تويتر
            if (customId.startsWith('twitter_')) {
                await this.handleTwitter(interaction, client, customId);
                return;
            }

            // جهات الاتصال
            if (customId.startsWith('contacts_')) {
                await this.handleContacts(interaction, client, customId);
                return;
            }

            // الواتساب
            if (customId.startsWith('whatsapp_')) {
                await this.handleWhatsApp(interaction, client, customId);
                return;
            }

            // المكالمات
            if (customId.startsWith('call_')) {
                await this.handleCalls(interaction, client, customId);
                return;
            }

            // الإعدادات
            if (customId.startsWith('settings_')) {
                await this.handleSettings(interaction, client, customId);
                return;
            }

            // أزرار الحماية
            if (customId.startsWith('security_')) {
                await this.handleSecurityAction(interaction, customId);
                return;
            }



            // قفل الجوال
            if (customId === 'phone_lock') {
                await this.lockPhone(interaction);
                return;
            }

        } catch (error) {
            console.error('خطأ في معالج الجوال:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: 'حدث خطأ في الجوال.', ephemeral: true });
            }
        }
    },

    // عرض الشاشة الرئيسية للجوال المحسنة
    async showPhoneHome(interaction, client) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const userPhone = await Phone.findOne({ userId, characterId });

        // تحميل الصورة الجاهزة للتطبيقات وإضافة المعلومات عليها
        const homeImage = await this.createCustomHomeImageEnhanced(interaction.user.username, userPhone);
        const attachment = new AttachmentBuilder(homeImage, { name: 'iphone16_home.png' });

        // إنشاء embed للجوال
        const embed = new EmbedBuilder()
            .setTitle(`📱 جوال ${interaction.user.username}`)
            .setDescription(`**رقم الجوال:** ${userPhone?.phoneNumber || 'غير محدد'}\n**الخلفية:** ${userPhone?.wallpaper || 'افتراضية'}\n\n**اختر التطبيق الذي تريد فتحه:**`)
            .setImage('attachment://iphone16_home.png')
            .setColor('#007AFF')
            .setFooter({ text: 'iPhone 16 Pro Max' })
            .setTimestamp();

        // منيو التطبيقات
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`phone_apps_${userId}`)
            .setPlaceholder('اختر التطبيق الذي تريد فتحه...')
            .addOptions([
                {
                    label: 'تويتر',
                    description: 'شارك أفكارك مع العالم',
                    value: 'twitter_home'
                },
                {
                    label: 'جهات الاتصال',
                    description: 'تواصل مع الأصدقاء',
                    value: 'contacts_home'
                },
                {
                    label: 'واتساب',
                    description: 'راسل من تحب',
                    value: 'whatsapp_home'
                },

                {
                    label: 'الإعدادات',
                    description: 'خصص جوالك',
                    value: 'settings_home'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row],
            ephemeral: true
        });
    },

    // معالج اختيارات المنيو
    async handleMenuSelection(interaction, client, selectedValue) {
        switch (selectedValue) {
            case 'twitter_home':
                await this.handleTwitter(interaction, client, 'twitter_home');
                break;
            case 'contacts_home':
                await this.handleContacts(interaction, client, 'contacts_home');
                break;
            case 'whatsapp_home':
                await this.handleWhatsApp(interaction, client, 'whatsapp_home');
                break;

            case 'settings_home':
                await this.handleSettings(interaction, client, 'settings_home');
                break;
            case 'phone_lock':
                await this.lockPhone(interaction);
                break;

            // معالجات تويتر
            case 'twitter_tweet':
                await this.handleTwitter(interaction, client, 'twitter_tweet');
                break;

            case 'twitter_search':
                await this.handleTwitter(interaction, client, 'twitter_search');
                break;
            case 'twitter_profile':
                await this.handleTwitter(interaction, client, 'twitter_profile');
                break;
            case 'twitter_edit_profile':
                await this.handleTwitter(interaction, client, 'twitter_edit_profile');
                break;

            // معالجات الإعدادات
            case 'settings_security':
                await this.handleSettings(interaction, client, 'settings_security');
                break;
            case 'settings_profile':
                await this.handleSettings(interaction, client, 'settings_profile');
                break;

            default:
                await interaction.reply({
                    content: 'خيار غير صحيح.',
                    ephemeral: true
                });
        }
    },

    // قفل الجوال
    async lockPhone(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🔒 تم قفل الجوال')
            .setDescription('تم قفل جوالك بنجاح!\nاستخدم `!جوال` لفتحه مرة أخرى.')
            .setColor('#8E8E93')
            .setTimestamp();

        await interaction.update({
            embeds: [embed],
            files: [],
            components: [],
            ephemeral: true
        });
    },

    // إنشاء صورة تغذية X المحسنة
    async createTwitterFeedImage(twitterAccount, tweets) {
        const canvas = createCanvas(480, 1000);
        const ctx = canvas.getContext('2d');

        // خلفية X متدرجة (أسود إلى رمادي داكن)
        const bgGradient = ctx.createLinearGradient(0, 0, 0, 1000);
        bgGradient.addColorStop(0, '#000000');
        bgGradient.addColorStop(0.5, '#0a0a0a');
        bgGradient.addColorStop(1, '#1a1a1a');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 480, 1000);

        // شريط العنوان مع تأثير زجاجي
        const headerGradient = ctx.createLinearGradient(0, 0, 0, 80);
        headerGradient.addColorStop(0, 'rgba(0,0,0,0.9)');
        headerGradient.addColorStop(1, 'rgba(0,0,0,0.7)');
        ctx.fillStyle = headerGradient;
        ctx.fillRect(0, 0, 480, 80);

        // شعار X مع تأثير متوهج
        ctx.shadowColor = '#1d9bf0';
        ctx.shadowBlur = 15;
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 36px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('𝕏', 240, 50);
        ctx.shadowBlur = 0;

        // معلومات الحساب مع خلفية محسنة
        const profileBg = ctx.createLinearGradient(0, 80, 0, 180);
        profileBg.addColorStop(0, 'rgba(29,155,240,0.1)');
        profileBg.addColorStop(1, 'rgba(29,155,240,0.05)');
        ctx.fillStyle = profileBg;
        ctx.fillRect(0, 80, 480, 100);

        // صورة الملف الشخصي مع إطار متوهج
        ctx.shadowColor = '#1d9bf0';
        ctx.shadowBlur = 10;
        ctx.fillStyle = '#1d9bf0';
        ctx.beginPath();
        ctx.arc(60, 130, 28, 0, 2 * Math.PI);
        ctx.fill();
        ctx.shadowBlur = 0;

        // أيقونة المستخدم
        ctx.font = '24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('USER', 60, 138);

        // معلومات المستخدم محسنة
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 22px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText(`@${twitterAccount.username}`, 100, 120);

        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#8b98a5';
        ctx.fillText(`${twitterAccount.followers?.length || 0} متابع • ${twitterAccount.following?.length || 0} متابَع`, 100, 145);

        // خط فاصل متوهج
        const lineGradient = ctx.createLinearGradient(0, 180, 480, 180);
        lineGradient.addColorStop(0, 'rgba(29,155,240,0)');
        lineGradient.addColorStop(0.5, 'rgba(29,155,240,0.5)');
        lineGradient.addColorStop(1, 'rgba(29,155,240,0)');
        ctx.strokeStyle = lineGradient;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(0, 180);
        ctx.lineTo(480, 180);
        ctx.stroke();

        let yPos = 220;

        if (tweets.length === 0) {
            // رسالة عدم وجود تغريدات محسنة
            ctx.fillStyle = '#8b98a5';
            ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد تغريدات حتى الآن', 240, 400);

            ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#71767b';
            ctx.fillText('ابدأ بكتابة أول تغريدة لك!', 240, 440);

            // أيقونة X كبيرة مع تأثير
            ctx.shadowColor = '#1d9bf0';
            ctx.shadowBlur = 20;
            ctx.font = '100px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = 'rgba(29,155,240,0.2)';
            ctx.fillText('X', 240, 350);
            ctx.shadowBlur = 0;
        } else {
            tweets.forEach((tweet, index) => {
                if (yPos > 750) return; // تجنب الخروج من الإطار

                // خلفية التغريدة مع تدرج
                const tweetBg = ctx.createLinearGradient(0, yPos - 10, 0, yPos + 110);
                tweetBg.addColorStop(0, index % 2 === 0 ? 'rgba(29,155,240,0.1)' : 'rgba(255,255,255,0.05)');
                tweetBg.addColorStop(1, index % 2 === 0 ? 'rgba(29,155,240,0.05)' : 'rgba(255,255,255,0.02)');
                ctx.fillStyle = tweetBg;
                ctx.roundRect(15, yPos - 10, 450, 120, 10);
                ctx.fill();

                // إطار التغريدة
                ctx.strokeStyle = 'rgba(29,155,240,0.3)';
                ctx.lineWidth = 1;
                ctx.roundRect(15, yPos - 10, 450, 120, 10);
                ctx.stroke();

                // صورة المستخدم الصغيرة
                ctx.fillStyle = '#1d9bf0';
                ctx.beginPath();
                ctx.arc(35, yPos + 15, 12, 0, 2 * Math.PI);
                ctx.fill();

                // أيقونة المستخدم
                ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.fillText('U', 35, yPos + 20);

                // اسم المستخدم محسن
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                ctx.textAlign = 'left';
                ctx.fillText(`@${tweet.username}`, 55, yPos + 15);

                // الوقت
                ctx.fillStyle = '#8b98a5';
                ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                const timeAgo = this.getTimeAgo(tweet.createdAt);
                ctx.fillText(`• ${timeAgo}`, 55, yPos + 32);

                // محتوى التغريدة محسن
                ctx.fillStyle = '#ffffff';
                ctx.font = '15px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                const words = tweet.content.split(' ');
                let line = '';
                let lineY = yPos + 55;

                words.forEach(word => {
                    const testLine = line + word + ' ';
                    const metrics = ctx.measureText(testLine);
                    if (metrics.width > 400 && line !== '') {
                        ctx.fillText(line, 25, lineY);
                        line = word + ' ';
                        lineY += 20;
                    } else {
                        line = testLine;
                    }
                });
                ctx.fillText(line, 25, lineY);

                // إحصائيات التغريدة محسنة
                ctx.fillStyle = '#8b98a5';
                ctx.font = 'bold 13px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                ctx.fillText(`LIKES ${tweet.likes?.length || 0}`, 25, yPos + 95);
                ctx.fillText(`RT ${tweet.retweets?.length || 0}`, 100, yPos + 95);

                // إضافة رمز للتغريدات مع صور
                if (tweet.hasImage) {
                    ctx.fillText(`IMG`, 175, yPos + 95);
                }

                yPos += 130;
            });
        }

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة تويتر حديثة بتصميم iOS
    async createTwitterFeedImageWithNavigation(twitterAccount, tweets, currentIndex = 0) {
        const canvas = createCanvas(430, 900);
        const ctx = canvas.getContext('2d');

        // خلفية تويتر داكنة ومريحة للعين
        const bgGradient = ctx.createLinearGradient(0, 0, 0, 900);
        bgGradient.addColorStop(0, '#000000');
        bgGradient.addColorStop(0.3, '#1C1C1E');
        bgGradient.addColorStop(0.7, '#2C2C2E');
        bgGradient.addColorStop(1, '#000000');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 430, 900);

        // شريط علوي داكن
        ctx.fillStyle = 'rgba(28, 28, 30, 0.9)';
        ctx.fillRect(0, 0, 430, 100);

        // عنوان تويتر
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 28px Arial';
        ctx.textAlign = 'center';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
        ctx.shadowBlur = 2;
        ctx.fillText('𝕏', 215, 50);
        ctx.shadowBlur = 0;

        // معلومات التنقل
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.font = '16px Arial';
        ctx.fillText(`التغريدة ${currentIndex + 1} من ${tweets.length}`, 215, 80);

        // عرض التغريدات بشكل متتالي (3 تغريدات)
        let yPosition = 120;
        const tweetsToShow = Math.min(3, tweets.length);

        for (let i = 0; i < tweetsToShow; i++) {
            const tweetIndex = (currentIndex + i) % tweets.length;
            const tweet = tweets[tweetIndex];
            const isMainTweet = i === 0;
            const tweetHeight = isMainTweet ? 200 : 150;

            // خلفية التغريدة
            ctx.fillStyle = isMainTweet ? 'rgba(29, 161, 242, 0.1)' : 'rgba(255, 255, 255, 0.05)';
            ctx.beginPath();
            ctx.roundRect(20, yPosition, 390, tweetHeight, 15);
            ctx.fill();

            // إطار التغريدة
            ctx.strokeStyle = isMainTweet ? '#1DA1F2' : 'rgba(255, 255, 255, 0.2)';
            ctx.lineWidth = isMainTweet ? 2 : 1;
            ctx.beginPath();
            ctx.roundRect(20, yPosition, 390, tweetHeight, 15);
            ctx.stroke();

            // أيقونة المستخدم
            ctx.fillStyle = isMainTweet ? '#1DA1F2' : '#8E8E93';
            ctx.beginPath();
            ctx.arc(45, yPosition + 30, 15, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('👤', 45, yPosition + 35);

            // اسم المستخدم
            ctx.fillStyle = '#FFFFFF';
            ctx.font = isMainTweet ? 'bold 16px Arial' : 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`@${tweet.username || 'مجهول'}`, 70, yPosition + 35);

            // وقت التغريدة
            const timeAgo = this.getTimeAgo(tweet.createdAt);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.font = '12px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(timeAgo, 400, yPosition + 35);

            // محتوى التغريدة
            ctx.fillStyle = '#FFFFFF';
            ctx.font = isMainTweet ? '16px -apple-system, BlinkMacSystemFont, Arial' : '14px -apple-system, BlinkMacSystemFont, Arial';
            ctx.textAlign = 'left';

            const maxWidth = 320;
            const lineHeight = isMainTweet ? 22 : 18;
            let textY = yPosition + 60;

            // تقسيم النص على عدة أسطر
            const words = tweet.content.split(' ');
            let line = '';

            for (let n = 0; n < words.length; n++) {
                const testLine = line + words[n] + ' ';
                const metrics = ctx.measureText(testLine);

                if (metrics.width > maxWidth && n > 0) {
                    ctx.fillText(line, 30, textY);
                    line = words[n] + ' ';
                    textY += lineHeight;
                } else {
                    line = testLine;
                }
            }
            ctx.fillText(line, 30, textY);

            // الإحصائيات والردود للتغريدة الرئيسية فقط
            if (isMainTweet) {
                textY += 30;

                // خط فاصل
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(30, textY);
                ctx.lineTo(400, textY);
                ctx.stroke();

                textY += 20;

                // إحصائيات التغريدة
                ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.font = 'bold 14px -apple-system, BlinkMacSystemFont, Arial';
                ctx.fillText(`❤️ ${tweet.likes?.length || 0}`, 30, textY);
                ctx.fillText(`🔄 ${tweet.retweets?.length || 0}`, 100, textY);

                // عدد الردود من نفس التغريدة
                const repliesCount = tweet.replies?.length || 0;
                ctx.fillText(`💬 ${repliesCount}`, 170, textY);

                // زر إظهار الردود إذا كان هناك ردود
                if (repliesCount > 0) {
                    ctx.fillStyle = 'rgba(29, 161, 242, 0.2)';
                    ctx.beginPath();
                    ctx.roundRect(250, textY - 15, 100, 25, 12);
                    ctx.fill();

                    ctx.strokeStyle = '#1DA1F2';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.roundRect(250, textY - 15, 100, 25, 12);
                    ctx.stroke();

                    ctx.fillStyle = '#1DA1F2';
                    ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('إظهار الردود', 300, textY - 2);
                    ctx.textAlign = 'left';
                }
            }

            yPosition += tweetHeight + 20;
        }

        // تعليمات التنقل في الأسفل
        if (tweets.length > 1) {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.font = '14px -apple-system, BlinkMacSystemFont, Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⬆️ ⬇️ للتنقل بين التغريدات', 215, 850);
        }

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة صفحة التسجيل في تويتر
    async createTwitterSignupImage() {
        const canvas = createCanvas(450, 900);
        const ctx = canvas.getContext('2d');

        // خلفية تويتر
        const gradient = ctx.createLinearGradient(0, 0, 0, 900);
        gradient.addColorStop(0, '#1DA1F2');
        gradient.addColorStop(1, '#0d8bd9');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 450, 900);

        // شعار تويتر الكبير
        ctx.font = 'bold 120px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('TWITTER', 225, 200);

        // عنوان الترحيب
        ctx.font = 'bold 48px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.fillText('مرحباً بك في تويتر', 225, 300);

        // رسالة الترحيب
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.fillText('انضم إلى المحادثة العالمية', 225, 350);

        // وصف الميزات
        const features = [
            'شارك أفكارك مع العالم',
            'تابع الأصدقاء والمشاهير',
            'شارك في المحادثات الشائعة',
            'اكتشف الأخبار والاتجاهات'
        ];

        ctx.font = '20px Arial';
        ctx.fillStyle = '#ffffff';
        let yPos = 420;

        features.forEach(feature => {
            ctx.fillText(feature, 225, yPos);
            yPos += 40;
        });

        // رسالة في الأسفل
        ctx.font = 'bold 18px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.fillText('استخدم المنيو أدناه للبدء', 225, 700);

        // خط زخرفي
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(125, 730);
        ctx.lineTo(325, 730);
        ctx.stroke();

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة الملف الشخصي في تويتر محسنة
    async createTwitterProfileImage(twitterAccount, userTweets) {
        const canvas = createCanvas(450, 900);
        const ctx = canvas.getContext('2d');

        // خلفية تويتر داكنة وأنيقة
        const gradient = ctx.createLinearGradient(0, 0, 0, 900);
        gradient.addColorStop(0, '#000000');
        gradient.addColorStop(0.3, '#1C1C1E');
        gradient.addColorStop(0.7, '#2C2C2E');
        gradient.addColorStop(1, '#000000');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 450, 900);

        // خلفية الغلاف
        const coverGradient = ctx.createLinearGradient(0, 0, 0, 200);
        coverGradient.addColorStop(0, '#1DA1F2');
        coverGradient.addColorStop(1, '#0d8bd9');
        ctx.fillStyle = coverGradient;
        ctx.fillRect(0, 0, 450, 200);

        // صورة الملف الشخصي (دائرة محسنة)
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(225, 150, 70, 0, 2 * Math.PI);
        ctx.fill();

        // إطار للصورة الشخصية
        ctx.strokeStyle = '#1DA1F2';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.arc(225, 150, 70, 0, 2 * Math.PI);
        ctx.stroke();

        // أيقونة المستخدم محسنة
        ctx.font = 'bold 50px Arial';
        ctx.fillStyle = '#1DA1F2';
        ctx.textAlign = 'center';
        ctx.fillText('👤', 225, 165);

        // اسم المستخدم مع تأثير
        ctx.font = 'bold 28px Arial';
        ctx.fillStyle = '#FFFFFF';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
        ctx.shadowBlur = 3;
        ctx.fillText(`@${twitterAccount.username}`, 225, 260);

        // الاسم المعروض
        ctx.font = 'bold 22px Arial';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.shadowBlur = 2;
        ctx.fillText(twitterAccount.displayName, 225, 290);

        // النبذة الشخصية محسنة
        ctx.font = '16px Arial';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.shadowBlur = 1;
        const bio = twitterAccount.bio || 'لا توجد نبذة شخصية';
        ctx.fillText(bio, 225, 320);
        ctx.shadowBlur = 0;

        // قسم الإحصائيات محسن
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(25, 350, 400, 120);

        ctx.font = 'bold 20px Arial';
        ctx.fillStyle = '#1DA1F2';
        ctx.fillText('📊 الإحصائيات', 225, 380);

        // الإحصائيات محسنة
        const stats = [
            { label: '👥 المتابعون', value: twitterAccount.followers.length },
            { label: '➕ المتابَعون', value: twitterAccount.following.length },
            { label: '📝 التغريدات', value: userTweets.length }
        ];

        ctx.font = '16px Arial';
        let yPos = 410;
        stats.forEach(stat => {
            ctx.fillStyle = '#FFFFFF';
            ctx.textAlign = 'left';
            ctx.fillText(stat.label, 50, yPos);

            ctx.fillStyle = '#1DA1F2';
            ctx.textAlign = 'right';
            ctx.fillText(stat.value.toString(), 400, yPos);
            yPos += 25;
        });

        // قسم التغريدات محسن
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(25, 490, 400, 350);

        ctx.font = 'bold 20px Arial';
        ctx.fillStyle = '#1DA1F2';
        ctx.textAlign = 'center';
        ctx.fillText('📝 آخر التغريدات', 225, 520);

        if (userTweets.length === 0) {
            ctx.font = '16px Arial';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.fillText('لم تقم بأي تغريدات بعد', 225, 560);
            ctx.fillText('ابدأ بكتابة أول تغريدة لك! ✨', 225, 590);
        } else {
            let tweetY = 560;

            userTweets.slice(0, 3).forEach((tweet, index) => {
                // خلفية التغريدة
                ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
                ctx.fillRect(35, tweetY - 15, 380, 80);

                // محتوى التغريدة محسن
                ctx.font = '14px Arial';
                ctx.fillStyle = '#FFFFFF';
                ctx.textAlign = 'left';

                let content = tweet.content;
                if (content.length > 60) {
                    content = content.substring(0, 60) + '...';
                }
                ctx.fillText(content, 45, tweetY + 10);

                // إحصائيات التغريدة محسنة
                const timeAgo = this.getTimeAgo(tweet.createdAt);
                ctx.font = '12px Arial';
                ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.fillText(`❤️ ${tweet.likes.length}  🔄 ${tweet.retweets.length}  🕒 ${timeAgo}`, 45, tweetY + 35);

                tweetY += 90;
            });
        }

        // رسالة ترحيبية في الأسفل
        ctx.fillStyle = 'rgba(29, 161, 242, 0.2)';
        ctx.fillRect(25, 850, 400, 40);

        ctx.font = 'bold 16px Arial';
        ctx.fillStyle = '#1DA1F2';
        ctx.textAlign = 'center';
        ctx.fillText('🎉 مرحباً بك في ملفك الشخصي! 🎉', 225, 875);

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة قائمة جهات الاتصال
    async createContactsListImage(contacts, page = 0) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية جهات الاتصال
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, 400, 800);

        // شريط العنوان
        ctx.fillStyle = '#34495e';
        ctx.fillRect(0, 0, 400, 60);

        // عنوان جهات الاتصال
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('جهات الاتصال', 200, 35);

        // خلفية القائمة
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(0, 60, 400, 740);

        const startIndex = page * 3;
        const endIndex = Math.min(startIndex + 3, contacts.length);
        const visibleContacts = contacts.slice(startIndex, endIndex);

        if (contacts.length === 0) {
            // رسالة عدم وجود جهات اتصال
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد جهات اتصال', 200, 300);

            ctx.font = '16px Arial';
            ctx.fillStyle = '#bdc3c7';
            ctx.fillText('اضغط "إضافة جهة اتصال"', 200, 330);
            ctx.fillText('لإضافة أول جهة اتصال', 200, 355);
        } else {
            // عرض معلومات الصفحة
            ctx.fillStyle = '#bdc3c7';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`الصفحة ${page + 1} من ${Math.ceil(contacts.length / 3)}`, 200, 85);
            ctx.fillText(`إجمالي جهات الاتصال: ${contacts.length}`, 200, 105);

            let yPos = 150;

            visibleContacts.forEach((contact, index) => {
                // خلفية جهة الاتصال
                ctx.fillStyle = index % 2 === 0 ? '#34495e' : '#2c3e50';
                ctx.fillRect(20, yPos - 20, 360, 120);

                // إطار جهة الاتصال
                ctx.strokeStyle = '#1abc9c';
                ctx.lineWidth = 2;
                ctx.roundRect(20, yPos - 20, 360, 120, 10);
                ctx.stroke();

                // أيقونة جهة الاتصال
                ctx.fillStyle = '#1abc9c';
                ctx.beginPath();
                ctx.arc(60, yPos + 20, 25, 0, 2 * Math.PI);
                ctx.fill();

                // رمز الشخص
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('USER', 60, yPos + 28);

                // اسم جهة الاتصال
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(contact.contactName, 100, yPos + 10);

                // رقم الهاتف
                ctx.fillStyle = '#bdc3c7';
                ctx.font = '14px Arial';
                ctx.fillText(contact.contactPhone, 100, yPos + 35);

                // حالة الاتصال
                ctx.fillStyle = '#27ae60';
                ctx.font = '12px Arial';
                ctx.fillText('متاح للاتصال', 100, yPos + 55);

                // أيقونة الاتصال
                ctx.fillStyle = '#27ae60';
                ctx.font = '20px Arial';
                ctx.textAlign = 'right';
                ctx.fillText('CALL', 360, yPos + 30);

                yPos += 140;
            });

            // مؤشرات التنقل
            if (contacts.length > 3) {
                ctx.fillStyle = '#95a5a6';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';

                if (page > 0) {
                    ctx.fillText('يوجد جهات اتصال أعلى', 200, 720);
                }

                if ((page + 1) * 3 < contacts.length) {
                    ctx.fillText('يوجد جهات اتصال أسفل', 200, 750);
                }
            }
        }

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة محادثات الواتساب
    async createWhatsAppChatsImage(chatsData) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية الواتساب
        ctx.fillStyle = '#0b141a';
        ctx.fillRect(0, 0, 400, 800);

        // شريط العنوان
        ctx.fillStyle = '#25D366';
        ctx.fillRect(0, 0, 400, 60);

        // عنوان الواتساب
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('واتساب', 200, 35);

        // خلفية المحادثات
        ctx.fillStyle = '#111b21';
        ctx.fillRect(0, 60, 400, 740);

        if (chatsData.length === 0) {
            // رسالة عدم وجود محادثات
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد محادثات', 200, 300);

            ctx.font = '16px Arial';
            ctx.fillStyle = '#8696a0';
            ctx.fillText('أضف جهات اتصال أولاً', 200, 330);
            ctx.fillText('من تطبيق الهاتف', 200, 355);
        } else {
            let yPos = 100;

            chatsData.forEach((chatData, index) => {
                if (yPos > 750) return; // تجنب الخروج من الإطار

                const { contact, lastMessage, unreadCount } = chatData;

                // خلفية المحادثة
                ctx.fillStyle = index % 2 === 0 ? '#1f2c34' : '#111b21';
                ctx.fillRect(10, yPos - 10, 380, 80);

                // صورة الملف الشخصي
                ctx.fillStyle = '#25D366';
                ctx.beginPath();
                ctx.arc(50, yPos + 25, 25, 0, 2 * Math.PI);
                ctx.fill();

                // رمز الشخص
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('USER', 50, yPos + 33);

                // اسم جهة الاتصال
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(contact.contactName, 85, yPos + 15);

                // آخر رسالة
                let lastMessageText = 'لا توجد رسائل';
                if (lastMessage) {
                    lastMessageText = lastMessage.content.length > 25
                        ? lastMessage.content.substring(0, 25) + '...'
                        : lastMessage.content;
                }

                ctx.fillStyle = '#8696a0';
                ctx.font = '14px Arial';
                ctx.fillText(lastMessageText, 85, yPos + 40);

                // وقت آخر رسالة
                if (lastMessage) {
                    const timeAgo = this.getTimeAgo(lastMessage.createdAt);
                    ctx.fillStyle = '#8696a0';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'right';
                    ctx.fillText(timeAgo, 380, yPos + 15);
                }

                // عداد الرسائل غير المقروءة
                if (unreadCount > 0) {
                    ctx.fillStyle = '#25D366';
                    ctx.beginPath();
                    ctx.arc(360, yPos + 35, 12, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = '#ffffff';
                    ctx.font = 'bold 10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(unreadCount.toString(), 360, yPos + 39);
                }

                // خط فاصل
                ctx.strokeStyle = '#2a3942';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(85, yPos + 60);
                ctx.lineTo(390, yPos + 60);
                ctx.stroke();

                yPos += 80;
            });
        }

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة محادثة الواتساب
    async createWhatsAppChatImage(contact, messages, userId) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية المحادثة
        ctx.fillStyle = '#0b141a';
        ctx.fillRect(0, 0, 400, 800);

        // شريط العنوان
        ctx.fillStyle = '#25D366';
        ctx.fillRect(0, 0, 400, 80);

        // صورة جهة الاتصال في الشريط
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(50, 40, 20, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = '#25D366';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('USER', 50, 47);

        // اسم جهة الاتصال
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(contact.contactName, 80, 35);

        // حالة الاتصال
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.fillText('متصل', 80, 55);

        // خلفية الرسائل
        ctx.fillStyle = '#111b21';
        ctx.fillRect(0, 80, 400, 720);

        if (messages.length === 0) {
            // رسالة عدم وجود رسائل
            ctx.fillStyle = '#8696a0';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد رسائل', 200, 350);
            ctx.fillText('ابدأ المحادثة!', 200, 380);
        } else {
            let yPos = 120;

            messages.forEach(message => {
                if (yPos > 750) return; // تجنب الخروج من الإطار

                const isMyMessage = message.senderId === userId;
                const messageWidth = Math.min(250, message.content.length * 8 + 40);

                // تحديد موقع الرسالة
                const xPos = isMyMessage ? 400 - messageWidth - 20 : 20;

                // خلفية الرسالة
                ctx.fillStyle = isMyMessage ? '#005c4b' : '#1f2c34';
                ctx.roundRect(xPos, yPos, messageWidth, 60, 10);
                ctx.fill();

                // محتوى الرسالة
                ctx.fillStyle = '#ffffff';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';

                // تقسيم النص إلى أسطر
                const words = message.content.split(' ');
                let line = '';
                let lineY = yPos + 20;

                words.forEach(word => {
                    const testLine = line + word + ' ';
                    const metrics = ctx.measureText(testLine);
                    if (metrics.width > messageWidth - 20 && line !== '') {
                        ctx.fillText(line, xPos + 10, lineY);
                        line = word + ' ';
                        lineY += 16;
                    } else {
                        line = testLine;
                    }
                });
                ctx.fillText(line, xPos + 10, lineY);

                // وقت الرسالة
                const timeStr = message.createdAt.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                ctx.fillStyle = '#8696a0';
                ctx.font = '10px Arial';
                ctx.textAlign = 'right';
                ctx.fillText(timeStr, xPos + messageWidth - 10, yPos + 50);

                // علامة القراءة للرسائل المرسلة
                if (isMyMessage) {
                    ctx.fillStyle = message.isRead ? '#53bdeb' : '#8696a0';
                    ctx.font = '12px Arial';
                    ctx.fillText('READ', xPos + messageWidth - 30, yPos + 50);
                }

                yPos += 80;
            });
        }

        return canvas.toBuffer('image/png');
    },

    // معالج تويتر
    async handleTwitter(interaction, client, customId) {
        const userId = interaction.user.id;

        // معالجة المنيو
        if (interaction.isStringSelectMenu() && customId.startsWith('twitter_menu_')) {
            const selectedValue = interaction.values[0];
            await this.handleTwitterMenuSelection(interaction, client, selectedValue);
            return;
        }

        if (customId === 'twitter_home') {
            // التحقق من وجود حساب تويتر
            let twitterAccount = await TwitterAccount.findOne({ userId });

            if (!twitterAccount) {
                await this.showTwitterSignup(interaction);
                return;
            }

            await this.showTwitterFeed(interaction, twitterAccount);
            return;
        }

        if (customId === 'twitter_signup') {
            await this.showTwitterSignupModal(interaction);
            return;
        }

        if (customId === 'twitter_tweet') {
            await this.showTweetModal(interaction);
            return;
        }

        if (customId === 'twitter_profile') {
            await this.showTwitterProfile(interaction);
            return;
        }

        if (customId === 'phone_home_from_twitter') {
            await this.showPhoneHome(interaction, client);
            return;
        }

        if (customId === 'twitter_refresh') {
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount);
            }
            return;
        }

        if (customId === 'twitter_edit_profile') {
            await this.showEditProfileModal(interaction);
            return;
        }

        // معالجة اللايك
        if (customId.startsWith('twitter_like_')) {
            const parts = customId.replace('twitter_like_', '').split('_');
            const tweetId = parts[0];
            const currentIndex = parseInt(parts[1]) || 0;
            await this.handleTwitterLike(interaction, tweetId, currentIndex);
            return;
        }

        // معالجة الريتويت
        if (customId.startsWith('twitter_retweet_')) {
            const parts = customId.replace('twitter_retweet_', '').split('_');
            const tweetId = parts[0];
            const currentIndex = parseInt(parts[1]) || 0;
            await this.handleTwitterRetweet(interaction, tweetId, currentIndex);
            return;
        }

        // معالجة إضافة صورة للحساب
        if (customId === 'twitter_add_photo') {
            await this.showAddPhotoModal(interaction);
            return;
        }

        // معالجة البحث في تويتر
        if (customId === 'twitter_search') {
            await this.showTwitterSearchModal(interaction);
            return;
        }



        // معالجة أزرار التنقل في تويتر
        if (customId.startsWith('twitter_nav_up_')) {
            const currentIndex = parseInt(customId.replace('twitter_nav_up_', ''));
            const newIndex = Math.max(0, currentIndex - 1);

            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount, newIndex);
            }
            return;
        }

        if (customId.startsWith('twitter_nav_down_')) {
            const currentIndex = parseInt(customId.replace('twitter_nav_down_', ''));
            const newIndex = currentIndex + 1;

            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount, newIndex);
            }
            return;
        }

        // معالجة أزرار التفاعل مع التغريدات (مع فهرس)
        if (customId.includes('twitter_like_') && customId.includes('_')) {
            const parts = customId.split('_');
            if (parts.length >= 4) {
                const tweetId = parts[2];
                const currentIndex = parseInt(parts[3]);
                await this.handleTweetLike(interaction, tweetId, currentIndex);
            }
            return;
        }

        if (customId.includes('twitter_retweet_') && customId.includes('_')) {
            const parts = customId.split('_');
            if (parts.length >= 4) {
                const tweetId = parts[2];
                const currentIndex = parseInt(parts[3]);
                await this.handleTweetRetweet(interaction, tweetId, currentIndex);
            }
            return;
        }

        if (customId.includes('twitter_reply_') && customId.includes('_')) {
            const parts = customId.split('_');
            if (parts.length >= 4) {
                const tweetId = parts[2];
                const currentIndex = parseInt(parts[3]);
                await this.showReplyModal(interaction, tweetId, currentIndex);
            }
            return;
        }

        // معالجة زر عرض الردود
        if (customId.includes('twitter_show_replies_') && customId.includes('_')) {
            const parts = customId.split('_');
            if (parts.length >= 5) {
                const tweetId = parts[3];
                const currentIndex = parseInt(parts[4]);
                await this.showTweetReplies(interaction, tweetId, currentIndex);
            }
            return;
        }

        // معالجة زر العودة للتغذية
        if (customId.includes('twitter_back_to_feed_')) {
            const currentIndex = parseInt(customId.replace('twitter_back_to_feed_', ''));
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount, currentIndex);
            }
            return;
        }

        // معالجة زر إظهار الردود
        if (customId.startsWith('twitter_show_replies_')) {
            const parts = customId.replace('twitter_show_replies_', '').split('_');
            const tweetId = parts[0];
            const currentIndex = parseInt(parts[1]) || 0;

            await this.showTweetReplies(interaction, tweetId, currentIndex);
            return;
        }
    },

    // معالج اختيارات منيو تويتر
    async handleTwitterMenuSelection(interaction, client, selectedValue) {
        const userId = interaction.user.id;

        switch (selectedValue) {
            case 'twitter_signup':
                await this.showTwitterSignupModal(interaction);
                break;
            case 'twitter_tweet':
                await this.showTweetModal(interaction);
                break;
            case 'twitter_profile':
                await this.showTwitterProfile(interaction);
                break;
            case 'twitter_edit_profile':
                await this.showEditProfileModal(interaction);
                break;
            case 'twitter_home':
                const twitterAccount = await TwitterAccount.findOne({ userId });
                if (twitterAccount) {
                    await this.showTwitterFeed(interaction, twitterAccount);
                }
                break;
            case 'twitter_refresh':
                const twitterAccount2 = await TwitterAccount.findOne({ userId });
                if (twitterAccount2) {
                    await this.showTwitterFeed(interaction, twitterAccount2);
                }
                break;
            case 'phone_home_from_twitter':
                await this.showPhoneHome(interaction, client);
                break;
            default:
                await interaction.reply({
                    content: 'خيار غير صحيح.',
                    ephemeral: true
                });
        }
    },

    // عرض صفحة التسجيل في تويتر
    async showTwitterSignup(interaction) {
        // إنشاء صورة صفحة التسجيل
        const signupImage = await this.createTwitterSignupImage();
        const attachment = new AttachmentBuilder(signupImage, { name: 'twitter_signup.png' });

        // منيو التسجيل
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`twitter_signup_menu_${interaction.user.id}`)
            .setPlaceholder('اختر ما تريد فعله...')
            .addOptions([
                {
                    label: 'إنشاء حساب جديد',
                    description: 'سجل في تويتر الآن',
                    value: 'twitter_signup'
                },
                {
                    label: 'العودة للرئيسية',
                    description: 'العودة لشاشة الجوال الرئيسية',
                    value: 'phone_home_from_twitter'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            files: [attachment],
            components: [row],
            ephemeral: true
        });
    },

    // عرض مودال التسجيل في تويتر
    async showTwitterSignupModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('twitter_signup_modal')
            .setTitle('إنشاء حساب تويتر جديد');

        const usernameInput = new TextInputBuilder()
            .setCustomId('twitter_username')
            .setLabel('اسم المستخدم (بدون @)')
            .setStyle(TextInputStyle.Short)
            .setMinLength(3)
            .setMaxLength(15)
            .setPlaceholder('مثال: ahmed_123')
            .setRequired(true);

        const displayNameInput = new TextInputBuilder()
            .setCustomId('twitter_display_name')
            .setLabel('الاسم المعروض')
            .setStyle(TextInputStyle.Short)
            .setMinLength(1)
            .setMaxLength(50)
            .setPlaceholder('مثال: أحمد محمد')
            .setRequired(true);

        const bioInput = new TextInputBuilder()
            .setCustomId('twitter_bio')
            .setLabel('النبذة الشخصية (اختياري)')
            .setStyle(TextInputStyle.Paragraph)
            .setMaxLength(160)
            .setPlaceholder('اكتب نبذة قصيرة عنك...')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(usernameInput);
        const secondActionRow = new ActionRowBuilder().addComponents(displayNameInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(bioInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);

        await interaction.showModal(modal);
    },

    // عرض تغذية تويتر مع نظام التنقل المحسن
    async showTwitterFeed(interaction, twitterAccount, currentTweetIndex = 0) {
        // جلب التغريدات الأصلية فقط (بدون إعادة النشر)
        const tweets = await Tweet.find({
            isRetweet: { $ne: true } // استبعاد إعادة النشر
        })
            .populate('userId', 'username')
            .sort({ createdAt: -1 })
            .limit(20); // زيادة العدد للتنقل

        // إنشاء صورة تغذية تويتر مع التغريدة المحددة
        const twitterImage = await this.createTwitterFeedImageWithNavigation(twitterAccount, tweets, currentTweetIndex);
        const attachment = new AttachmentBuilder(twitterImage, { name: 'twitter_feed.png' });

        // منيو تويتر المحسن
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`twitter_menu_${interaction.user.id}`)
            .setPlaceholder('اختر ما تريد فعله في X...')
            .addOptions([
                {
                    label: 'تغريدة جديدة',
                    description: 'اكتب تغريدة نصية',
                    value: 'twitter_tweet'
                },
                {
                    label: 'البحث',
                    description: 'ابحث عن تغريدات معينة',
                    value: 'twitter_search'
                },
                {
                    label: 'ملفي الشخصي',
                    description: 'عرض وتعديل ملفك الشخصي',
                    value: 'twitter_profile'
                },
                {
                    label: 'تحديث التغذية',
                    description: 'تحديث آخر التغريدات',
                    value: 'twitter_refresh'
                },
                {
                    label: 'العودة للرئيسية',
                    description: 'العودة لشاشة الجوال',
                    value: 'phone_home_from_twitter'
                }
            ]);

        const row1 = new ActionRowBuilder().addComponents(selectMenu);

        // أزرار التنقل بين التغريدات
        const navigationRow = new ActionRowBuilder();

        if (currentTweetIndex > 0) {
            navigationRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`twitter_nav_up_${currentTweetIndex}`)
                    .setLabel('⬆️ السابق')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        if (currentTweetIndex < tweets.length - 1) {
            navigationRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`twitter_nav_down_${currentTweetIndex}`)
                    .setLabel('⬇️ التالي')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        // أزرار التفاعل للتغريدة الحالية
        const interactionRow = new ActionRowBuilder();

        if (tweets.length > 0 && tweets[currentTweetIndex]) {
            const currentTweet = tweets[currentTweetIndex];
            const isLiked = currentTweet.likes?.includes(interaction.user.id);
            const isRetweeted = currentTweet.retweets?.includes(interaction.user.id);

            interactionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`twitter_like_${currentTweet._id}_${currentTweetIndex}`)
                    .setLabel(`❤️ ${currentTweet.likes?.length || 0}`)
                    .setStyle(isLiked ? ButtonStyle.Danger : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`twitter_retweet_${currentTweet._id}_${currentTweetIndex}`)
                    .setLabel(`🔄 ${currentTweet.retweets?.length || 0}`)
                    .setStyle(isRetweeted ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId(`twitter_reply_${currentTweet._id}_${currentTweetIndex}`)
                    .setLabel('💬 رد')
                    .setStyle(ButtonStyle.Primary)
            );
        }

        // صف إضافي لإظهار الردود
        const repliesRow = new ActionRowBuilder();
        if (tweets.length > 0 && tweets[currentTweetIndex]) {
            const currentTweet = tweets[currentTweetIndex];

            repliesRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`twitter_show_replies_${currentTweet._id}_${currentTweetIndex}`)
                    .setLabel('📋 إظهار الردود')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        // تحديد المكونات
        let components = [row1];
        if (navigationRow.components.length > 0) components.push(navigationRow);
        if (interactionRow.components.length > 0) components.push(interactionRow);
        if (repliesRow.components.length > 0) components.push(repliesRow);

        // إضافة معلومات التنقل للـ embed
        const embed = new EmbedBuilder()
            .setTitle('تويتر')
            .setDescription(`التغريدة ${currentTweetIndex + 1} من ${tweets.length}`)
            .setImage('attachment://twitter_feed.png')
            .setColor('#1DA1F2');

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: components,
            ephemeral: true
        });
    },

    // معالج الإعجاب بالتغريدة مع التنقل المحسن
    async handleTweetLike(interaction, tweetId, currentIndex) {
        try {
            const userId = interaction.user.id;
            const tweet = await Tweet.findById(tweetId);

            if (!tweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            // تحديث الإعجاب
            const isLiked = tweet.likes.includes(userId);
            if (isLiked) {
                tweet.likes.pull(userId);
                await tweet.save();
            } else {
                tweet.likes.push(userId);
                await tweet.save();
            }

            // تحديث العرض فوراً
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount, currentIndex);
            }

        } catch (error) {
            console.error('خطأ في الإعجاب:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // معالج إعادة التغريد مع التنقل المحسن
    async handleTweetRetweet(interaction, tweetId, currentIndex) {
        try {
            const userId = interaction.user.id;
            const originalTweet = await Tweet.findById(tweetId);

            if (!originalTweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            // تحديث إعادة التغريد
            const isRetweeted = originalTweet.retweets.includes(userId);
            if (isRetweeted) {
                originalTweet.retweets.pull(userId);
                await originalTweet.save();

                // حذف تغريدة إعادة التغريد إذا وجدت
                await Tweet.deleteOne({
                    userId,
                    isRetweet: true,
                    originalTweetId: tweetId
                });
            } else {
                // إضافة إعادة التغريد
                originalTweet.retweets.push(userId);
                await originalTweet.save();

                // إنشاء تغريدة جديدة كإعادة تغريد
                const retweetContent = `أعاد تغريد: ${originalTweet.content}`;

                const newTweet = new Tweet({
                    userId,
                    content: retweetContent,
                    isRetweet: true,
                    originalTweetId: tweetId,
                    createdAt: new Date()
                });

                await newTweet.save();
            }

            // تحديث العرض فوراً
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                await this.showTwitterFeed(interaction, twitterAccount, currentIndex);
            }

        } catch (error) {
            console.error('خطأ في إعادة التغريد:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // عرض نافذة الرد على التغريدة
    async showReplyModal(interaction, tweetId, currentIndex) {
        try {
            // جلب التغريدة الأصلية لعرض معلوماتها
            const originalTweet = await Tweet.findById(tweetId).populate('userId', 'username');

            if (!originalTweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            const modal = new ModalBuilder()
                .setCustomId(`twitter_reply_modal_${tweetId}_${currentIndex}`)
                .setTitle(`رد على @${originalTweet.username || 'مجهول'}`);

            const replyInput = new TextInputBuilder()
                .setCustomId('reply_content')
                .setLabel('اكتب ردك')
                .setStyle(TextInputStyle.Paragraph)
                .setMinLength(1)
                .setMaxLength(280)
                .setPlaceholder(`رداً على: ${originalTweet.content.substring(0, 50)}...`)
                .setRequired(true);

            const firstActionRow = new ActionRowBuilder().addComponents(replyInput);
            modal.addComponents(firstActionRow);

            await interaction.showModal(modal);
        } catch (error) {
            console.error('خطأ في عرض نافذة الرد:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // عرض الردود على التغريدة
    async showTweetReplies(interaction, tweetId, currentIndex) {
        try {
            const userId = interaction.user.id;

            // جلب التغريدة الأصلية
            const originalTweet = await Tweet.findById(tweetId).populate('userId', 'username');
            if (!originalTweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            // الحصول على الردود من نفس التغريدة
            const replies = originalTweet.replies || [];

            // إنشاء صورة الردود
            const repliesImage = await this.createTweetRepliesImage(originalTweet, replies);

            const embed = new EmbedBuilder()
                .setTitle('💬 الردود على التغريدة')
                .setDescription(`${replies.length} رد على هذه التغريدة`)
                .setImage('attachment://tweet_replies.png')
                .setColor('#1DA1F2');

            // أزرار التحكم
            const actionRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`twitter_reply_${tweetId}_${currentIndex}`)
                        .setLabel('إضافة رد')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(`twitter_back_to_feed_${currentIndex}`)
                        .setLabel('العودة للتغذية')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                files: [{ attachment: repliesImage, name: 'tweet_replies.png' }],
                components: [actionRow],
                ephemeral: true
            });

        } catch (error) {
            console.error('خطأ في عرض الردود:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // عرض مودال التغريدة
    async showTweetModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('twitter_tweet_modal')
            .setTitle('تغريدة جديدة');

        const tweetInput = new TextInputBuilder()
            .setCustomId('tweet_content')
            .setLabel('ماذا يحدث؟')
            .setStyle(TextInputStyle.Paragraph)
            .setMinLength(1)
            .setMaxLength(280)
            .setPlaceholder('اكتب تغريدتك هنا...')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(tweetInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // عرض الملف الشخصي في تويتر
    async showTwitterProfile(interaction) {
        const userId = interaction.user.id;
        const twitterAccount = await TwitterAccount.findOne({ userId });

        if (!twitterAccount) {
            await interaction.reply({
                content: 'لا يمكن العثور على حسابك في تويتر.',
                ephemeral: true
            });
            return;
        }

        // جلب تغريدات المستخدم
        const userTweets = await Tweet.find({ userId }).sort({ createdAt: -1 }).limit(5);

        // إنشاء صورة الملف الشخصي
        const profileImage = await this.createTwitterProfileImage(twitterAccount, userTweets);
        const attachment = new AttachmentBuilder(profileImage, { name: 'twitter_profile.png' });

        // منيو الملف الشخصي
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`twitter_profile_menu_${userId}`)
            .setPlaceholder('اختر ما تريد فعله...')
            .addOptions([
                {
                    label: 'تعديل الملف الشخصي',
                    description: 'تعديل الاسم والنبذة الشخصية',
                    value: 'twitter_edit_profile'
                },
                {
                    label: 'العودة لتويتر',
                    description: 'العودة لتغذية تويتر',
                    value: 'twitter_home'
                },
                {
                    label: 'العودة للرئيسية',
                    description: 'العودة لشاشة الجوال الرئيسية',
                    value: 'phone_home_from_twitter'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.update({
            files: [attachment],
            components: [row],
            ephemeral: true
        });
    },

    // عرض مودال تعديل الملف الشخصي
    async showEditProfileModal(interaction) {
        const userId = interaction.user.id;
        const twitterAccount = await TwitterAccount.findOne({ userId });

        if (!twitterAccount) {
            await interaction.reply({
                content: 'لا يمكن العثور على حسابك في تويتر.',
                ephemeral: true
            });
            return;
        }

        const modal = new ModalBuilder()
            .setCustomId('twitter_edit_profile_modal')
            .setTitle('تعديل الملف الشخصي');

        const displayNameInput = new TextInputBuilder()
            .setCustomId('edit_display_name')
            .setLabel('الاسم المعروض')
            .setStyle(TextInputStyle.Short)
            .setMinLength(1)
            .setMaxLength(50)
            .setValue(twitterAccount.displayName)
            .setRequired(true);

        const bioInput = new TextInputBuilder()
            .setCustomId('edit_bio')
            .setLabel('النبذة الشخصية')
            .setStyle(TextInputStyle.Paragraph)
            .setMaxLength(160)
            .setValue(twitterAccount.bio || '')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(displayNameInput);
        const secondActionRow = new ActionRowBuilder().addComponents(bioInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    },

    // معالج جهات الاتصال
    async handleContacts(interaction, client, customId) {
        const userId = interaction.user.id;

        if (customId === 'contacts_home') {
            await this.showContactsList(interaction);
            return;
        }

        if (customId === 'contacts_add') {
            await this.showAddContactModal(interaction);
            return;
        }

        if (customId.startsWith('call_contact_')) {
            const contactUserId = customId.replace('call_contact_', '');
            await this.initiateCall(interaction, contactUserId);
            return;
        }

        if (customId === 'contacts_refresh') {
            await this.showContactsList(interaction);
            return;
        }

        if (customId.startsWith('contacts_page_')) {
            const page = parseInt(customId.replace('contacts_page_', ''));
            await this.showContactsList(interaction, page);
            return;
        }

        if (customId.startsWith('contact_call_')) {
            await interaction.reply({
                content: 'ميزة المكالمات معطلة مؤقتاً للصيانة!',
                ephemeral: true
            });
            return;
        }
    },

    // عرض قائمة جهات الاتصال
    async showContactsList(interaction, page = 0) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const contacts = await Contact.find({
            ownerId: userId,
            ownerCharacterId: characterId
        }).sort({ contactName: 1 });

        // إنشاء صورة جهات الاتصال
        const contactsImage = await this.createContactsListImage(contacts, page);
        const attachment = new AttachmentBuilder(contactsImage, { name: 'contacts.png' });

        const embed = new EmbedBuilder()
            .setTitle('جهات الاتصال')
            .setDescription('اختر جهة الاتصال للتواصل معها')
            .setImage('attachment://contacts.png')
            .setColor('#34495e');

        const rows = [];

        // أزرار التنقل والإضافة
        const mainRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('contacts_add')
                    .setLabel('إضافة جهة اتصال')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('phone_home')
                    .setLabel('العودة للرئيسية')
                    .setStyle(ButtonStyle.Secondary)

            );
        rows.push(mainRow);

        // أزرار التنقل بين الصفحات
        if (contacts.length > 3) {
            const navRow = new ActionRowBuilder();

            if (page > 0) {
                navRow.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`contacts_page_${page - 1}`)
                        .setLabel('السابق')
                        .setStyle(ButtonStyle.Secondary)
                );
            }

            if ((page + 1) * 3 < contacts.length) {
                navRow.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`contacts_page_${page + 1}`)
                        .setLabel('التالي')
                        .setStyle(ButtonStyle.Secondary)
                );
            }

            if (navRow.components.length > 0) {
                rows.push(navRow);
            }
        }

        // أزرار جهات الاتصال مع خيارات متعددة
        const startIndex = page * 3;
        const endIndex = Math.min(startIndex + 3, contacts.length);
        const visibleContacts = contacts.slice(startIndex, endIndex);

        if (visibleContacts.length > 0) {
            // صف لكل جهة اتصال مع أزرار المكالمة والرسائل
            visibleContacts.forEach((contact, index) => {
                const contactRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`contact_call_${contact.contactUserId}`)
                            .setLabel(`📞 ${contact.contactName}`)
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId(`whatsapp_chat_${contact.contactUserId}`)
                            .setLabel(`💬 رسالة`)
                            .setStyle(ButtonStyle.Primary)
                    );
                rows.push(contactRow);
            });
        }

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: rows,
            ephemeral: true
        });
    },

    // بدء مكالمة مع جهة اتصال
    async initiateCall(interaction, contactUserId) {
        try {
            const userId = interaction.user.id;

            // الحصول على الشخصية النشطة
            const activeCharacter = await require('../models/activeCh').findOne({ userId });
            const characterId = activeCharacter ? activeCharacter.characterId : userId;

            // البحث عن جهة الاتصال
            const contact = await Contact.findOne({
                ownerId: userId,
                ownerCharacterId: characterId,
                contactUserId: contactUserId
            });

            if (!contact) {
                await interaction.reply({
                    content: 'جهة الاتصال غير موجودة!',
                    ephemeral: true
                });
                return;
            }

            // إنشاء صورة المكالمة
            const callImage = await this.createCallImage(contact.contactName, 'outgoing');
            const attachment = new AttachmentBuilder(callImage, { name: 'call.png' });

            const embed = new EmbedBuilder()
                .setTitle('📞 مكالمة جارية')
                .setDescription(`**الاتصال بـ:** ${contact.contactName}\n**الحالة:** جاري الاتصال...`)
                .setImage('attachment://call.png')
                .setColor('#34C759');

            // أزرار المكالمة
            const callRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`call_end_${contactUserId}`)
                        .setLabel('إنهاء المكالمة')
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('contacts_home')
                        .setLabel('العودة لجهات الاتصال')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                files: [attachment],
                components: [callRow],
                ephemeral: true
            });

            // محاكاة المكالمة - إرسال إشعار للمستقبل
            try {
                const receiverUser = await interaction.client.users.fetch(contactUserId);
                if (receiverUser) {
                    const callNotificationEmbed = new EmbedBuilder()
                        .setTitle('📞 مكالمة واردة')
                        .setDescription(`**من:** ${interaction.user.username}\n**الوقت:** ${new Date().toLocaleTimeString('ar-SA')}`)
                        .setColor('#FF3B30');

                    const answerRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`call_answer_${userId}`)
                                .setLabel('رد على المكالمة')
                                .setStyle(ButtonStyle.Success),
                            new ButtonBuilder()
                                .setCustomId(`call_decline_${userId}`)
                                .setLabel('رفض المكالمة')
                                .setStyle(ButtonStyle.Danger)
                        );

                    await receiverUser.send({
                        embeds: [callNotificationEmbed],
                        components: [answerRow]
                    });
                }
            } catch (error) {
                console.log('لا يمكن إرسال إشعار المكالمة للمستقبل');
            }

            // محاكاة انتهاء المكالمة بعد 30 ثانية
            setTimeout(async () => {
                try {
                    const endCallEmbed = new EmbedBuilder()
                        .setTitle('📞 انتهت المكالمة')
                        .setDescription(`**المكالمة مع:** ${contact.contactName}\n**المدة:** 30 ثانية\n**الحالة:** انتهت`)
                        .setColor('#8E8E93');

                    const backRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId('contacts_home')
                                .setLabel('العودة لجهات الاتصال')
                                .setStyle(ButtonStyle.Primary)
                        );

                    await interaction.editReply({
                        embeds: [endCallEmbed],
                        files: [],
                        components: [backRow]
                    });
                } catch (error) {
                    console.log('خطأ في إنهاء المكالمة:', error);
                }
            }, 30000);

        } catch (error) {
            console.error('خطأ في بدء المكالمة:', error);
            await interaction.reply({
                content: 'حدث خطأ أثناء بدء المكالمة!',
                ephemeral: true
            });
        }
    },

    // إنشاء صورة المكالمة
    async createCallImage(contactName, callType) {
        const canvas = createCanvas(400, 600);
        const ctx = canvas.getContext('2d');

        // خلفية المكالمة
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        if (callType === 'outgoing') {
            gradient.addColorStop(0, '#34C759');
            gradient.addColorStop(1, '#30A14E');
        } else {
            gradient.addColorStop(0, '#FF3B30');
            gradient.addColorStop(1, '#D70015');
        }
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 600);

        // صورة جهة الاتصال (دائرة)
        ctx.fillStyle = 'rgba(255,255,255,0.3)';
        ctx.beginPath();
        ctx.arc(200, 200, 80, 0, 2 * Math.PI);
        ctx.fill();

        // أيقونة الشخص
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 60px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('👤', 200, 220);

        // اسم جهة الاتصال
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.fillText(contactName, 200, 320);

        // حالة المكالمة
        ctx.fillStyle = 'rgba(255,255,255,0.8)';
        ctx.font = '16px Arial';
        if (callType === 'outgoing') {
            ctx.fillText('جاري الاتصال...', 200, 350);
        } else {
            ctx.fillText('مكالمة واردة', 200, 350);
        }

        // الوقت
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        ctx.fillText(timeString, 200, 380);

        // أيقونة الهاتف
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 40px Arial';
        ctx.fillText('📞', 200, 480);

        return canvas.toBuffer('image/png');
    },

    // عرض مودال إضافة جهة اتصال
    async showAddContactModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('contacts_add_modal')
            .setTitle('إضافة جهة اتصال جديدة');

        const phoneInput = new TextInputBuilder()
            .setCustomId('contact_phone')
            .setLabel('رقم الجوال')
            .setStyle(TextInputStyle.Short)
            .setMinLength(10)
            .setMaxLength(20)
            .setPlaceholder('+966501234567')
            .setRequired(true);

        const nameInput = new TextInputBuilder()
            .setCustomId('contact_name')
            .setLabel('اسم جهة الاتصال')
            .setStyle(TextInputStyle.Short)
            .setMinLength(1)
            .setMaxLength(50)
            .setPlaceholder('أحمد محمد')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(phoneInput);
        const secondActionRow = new ActionRowBuilder().addComponents(nameInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    },

    // بدء مكالمة
    async initiateCall(interaction, contactUserId) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة للمتصل
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        // جلب معلومات المتصل
        const callerPhone = await Phone.findOne({ userId, characterId });
        const callerContact = await Contact.findOne({
            ownerId: userId,
            ownerCharacterId: characterId,
            contactUserId
        });

        // الحصول على الشخصية النشطة للمستقبل
        const receiverActiveCharacter = await require('../models/activeCh').findOne({ userId: contactUserId });
        const receiverCharacterId = receiverActiveCharacter ? receiverActiveCharacter.characterId : contactUserId;

        // جلب معلومات المستقبل
        const receiverPhone = await Phone.findOne({ userId: contactUserId, characterId: receiverCharacterId });

        if (!callerPhone || !receiverPhone || !callerContact) {
            await interaction.reply({
                content: 'لا يمكن إجراء المكالمة. تأكد من صحة البيانات.',
                ephemeral: true
            });
            return;
        }

        // إنشاء مكالمة جديدة
        const callId = Date.now().toString();
        const newCall = new Call({
            callId,
            callerId: userId,
            callerCharacterId: characterId,
            receiverId: contactUserId,
            receiverCharacterId: receiverCharacterId,
            callerName: interaction.user.username,
            receiverName: callerContact.contactName,
            status: 'calling'
        });

        await newCall.save();

        // عرض شاشة المكالمة للمتصل
        await this.showCallingScreen(interaction, newCall, 'caller');

        // إرسال إشعار للمستقبل
        try {
            const receiverUser = await interaction.client.users.fetch(contactUserId);
            if (receiverUser) {
                const callEmbed = new EmbedBuilder()
                    .setTitle('مكالمة واردة')
                    .setDescription(`**${interaction.user.username}** يتصل بك الآن!`)
                    .setColor('#e74c3c')
                    .setTimestamp();

                const callRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`call_answer_${callId}`)
                            .setLabel('رد')
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId(`call_decline_${callId}`)
                            .setLabel('رفض')
                            .setStyle(ButtonStyle.Danger)
                    );

                await receiverUser.send({
                    embeds: [callEmbed],
                    components: [callRow]
                });
            }
        } catch (error) {
            console.log('لا يمكن إرسال إشعار المكالمة للمستقبل');
        }
    },

    // عرض شاشة المكالمة
    async showCallingScreen(interaction, call, userType) {
        let title, description, color;

        if (userType === 'caller') {
            title = 'جاري الاتصال...';
            description = `الاتصال بـ **${call.receiverName}**\n\nجاري الانتظار...`;
            color = '#3498db';
        } else {
            title = 'مكالمة واردة';
            description = `**${call.callerName}** يتصل بك\n\nاضغط رد للإجابة`;
            color = '#e74c3c';
        }

        const embed = new EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(color)
            .setTimestamp();

        const row = new ActionRowBuilder();

        if (userType === 'caller') {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`call_end_${call.callId}`)
                    .setLabel('إنهاء المكالمة')
                    .setStyle(ButtonStyle.Danger)
            );
        } else {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`call_answer_${call.callId}`)
                    .setLabel('رد')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`call_decline_${call.callId}`)
                    .setLabel('رفض')
                    .setStyle(ButtonStyle.Danger)
            );
        }

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });
        } else {
            await interaction.update({
                embeds: [embed],
                components: [row]
            });
        }
    },

    // معالج الواتساب
    async handleWhatsApp(interaction, client, customId) {
        const userId = interaction.user.id;

        if (customId === 'whatsapp_home') {
            await this.showWhatsAppChats(interaction);
            return;
        }

        if (customId.startsWith('whatsapp_chat_') || customId.startsWith('whatsapp_open_chat_')) {
            const contactUserId = customId.replace('whatsapp_chat_', '').replace('whatsapp_open_chat_', '');
            await this.showWhatsAppChatEnhanced(interaction, contactUserId);
            return;
        }

        if (customId.startsWith('whatsapp_send_')) {
            const contactUserId = customId.replace('whatsapp_send_', '');
            await this.showWhatsAppSendModal(interaction, contactUserId);
            return;
        }

        if (customId.startsWith('whatsapp_send_image_')) {
            const contactUserId = customId.replace('whatsapp_send_image_', '');
            await this.showWhatsAppImageModal(interaction, contactUserId);
            return;
        }

        if (customId === 'whatsapp_refresh') {
            await this.showWhatsAppChats(interaction);
            return;
        }


    },

    // عرض محادثات الواتساب المحسنة
    async showWhatsAppChats(interaction) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const contacts = await Contact.find({
            ownerId: userId,
            ownerCharacterId: characterId
        }).sort({ contactName: 1 });

        // جلب آخر الرسائل لكل محادثة
        const chatsWithMessages = [];
        for (const contact of contacts) {
            const lastMessage = await WhatsAppMessage.findOne({
                $or: [
                    { senderId: userId, receiverId: contact.contactUserId },
                    { senderId: contact.contactUserId, receiverId: userId }
                ]
            }).sort({ createdAt: -1 });

            chatsWithMessages.push({
                contact,
                lastMessage,
                unreadCount: await WhatsAppMessage.countDocuments({
                    senderId: contact.contactUserId,
                    receiverId: userId,
                    isRead: false
                })
            });
        }

        // إنشاء صورة الواتساب المحسنة
        const whatsappHomeImage = await this.createWhatsAppHomeImageEnhanced(chatsWithMessages);
        const homeAttachment = new AttachmentBuilder(whatsappHomeImage, { name: 'whatsapp_home.png' });

        // إنشاء أزرار المحادثات
        const chatButtons = [];
        for (let i = 0; i < Math.min(chatsWithMessages.length, 5); i++) {
            const chatData = chatsWithMessages[i];
            const row = new ActionRowBuilder();

            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`whatsapp_open_chat_${chatData.contact.contactUserId}`)
                    .setLabel(`${chatData.contact.contactName}${chatData.unreadCount > 0 ? ` (${chatData.unreadCount})` : ''}`)
                    .setStyle(chatData.unreadCount > 0 ? ButtonStyle.Primary : ButtonStyle.Secondary)
            );

            chatButtons.push(row);
        }

        // إنشاء embed للواتساب
        const embed = new EmbedBuilder()
            .setTitle('واتساب')
            .setDescription(`لديك ${chatsWithMessages.length} محادثة`)
            .setImage('attachment://whatsapp_home.png')
            .setColor('#25D366');

        // الصف الأساسي
        const mainRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('whatsapp_refresh')
                    .setLabel('تحديث')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('phone_home')
                    .setLabel('العودة للرئيسية')
                    .setStyle(ButtonStyle.Secondary)
            );

        // تجميع جميع المكونات
        const allComponents = [mainRow, ...chatButtons];

        await interaction.update({
            embeds: [embed],
            files: [homeAttachment],
            components: allComponents.slice(0, 5), // Discord يدعم 5 صفوف فقط
            ephemeral: true
        });
    },

    // عرض محادثة واتساب محسنة مع جميع الرسائل
    async showWhatsAppChatEnhanced(interaction, contactUserId, messageIndex = null) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        // البحث عن جهة الاتصال
        const contact = await Contact.findOne({
            ownerId: userId,
            ownerCharacterId: characterId,
            contactUserId: contactUserId
        });

        if (!contact) {
            await interaction.reply({
                content: 'جهة الاتصال غير موجودة.',
                ephemeral: true
            });
            return;
        }

        // جلب جميع الرسائل في المحادثة
        const messages = await WhatsAppMessage.find({
            $or: [
                { senderId: userId, receiverId: contactUserId },
                { senderId: contactUserId, receiverId: userId }
            ]
        }).sort({ createdAt: 1 }); // ترتيب تصاعدي

        // عرض المحادثة كاملة من آخر رسالة
        const visibleMessages = messages;

        // إنشاء صورة المحادثة
        const chatImage = await this.createWhatsAppChatImageEnhanced(
            contact,
            visibleMessages,
            userId,
            messages.length
        );

        const embed = new EmbedBuilder()
            .setTitle(`💬 ${contact.contactName}`)
            .setDescription(`المحادثة كاملة - ${messages.length} رسالة`)
            .setImage('attachment://whatsapp_chat.png')
            .setColor('#25D366');

        // أزرار الإجراءات
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`whatsapp_send_${contactUserId}`)
                    .setLabel('إرسال رسالة')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`whatsapp_send_image_${contactUserId}`)
                    .setLabel('إرسال صورة')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('whatsapp_home')
                    .setLabel('العودة للمحادثات')
                    .setStyle(ButtonStyle.Secondary)
            );

        const components = [actionRow];

        await interaction.update({
            embeds: [embed],
            files: [{ attachment: chatImage, name: 'whatsapp_chat.png' }],
            components,
            ephemeral: true
        });

        // تحديد الرسائل كمقروءة
        await WhatsAppMessage.updateMany(
            { senderId: contactUserId, receiverId: userId, isRead: false },
            { isRead: true }
        );
    },

    // طلب إرسال صورة في الواتساب
    async showWhatsAppImageModal(interaction, contactUserId) {
        // حفظ معلومات الطلب
        if (!global.pendingWhatsAppImage) {
            global.pendingWhatsAppImage = {};
        }

        global.pendingWhatsAppImage[interaction.user.id] = {
            contactUserId: contactUserId,
            timestamp: Date.now()
        };

        await interaction.reply({
            content: '📸 **إرسال صورة في الواتساب**\n\n' +
                     '🔹 أرسل الصورة في رسالة خاصة\n' +
                     '🔹 سيتم إرسالها تلقائياً في المحادثة\n' +
                     '🔹 يمكنك إضافة نص مع الصورة',
            ephemeral: true
        });
    },

    // عرض محادثة واتساب محددة (الدالة القديمة للتوافق)
    async showWhatsAppChat(interaction, contactUserId) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        // جلب معلومات جهة الاتصال
        const contact = await Contact.findOne({
            ownerId: userId,
            ownerCharacterId: characterId,
            contactUserId
        });
        if (!contact) {
            await interaction.reply({
                content: 'جهة الاتصال غير موجودة.',
                ephemeral: true
            });
            return;
        }

        // جلب الرسائل
        const messages = await WhatsAppMessage.find({
            $or: [
                { senderId: userId, receiverId: contactUserId },
                { senderId: contactUserId, receiverId: userId }
            ]
        }).sort({ createdAt: 1 }).limit(10);

        // تحديد الرسائل كمقروءة
        await WhatsAppMessage.updateMany(
            { senderId: contactUserId, receiverId: userId, isRead: false },
            { isRead: true }
        );

        // إنشاء صورة المحادثة
        const chatImage = await this.createWhatsAppChatImage(contact, messages, userId);
        const attachment = new AttachmentBuilder(chatImage, { name: 'whatsapp_chat.png' });

        const embed = new EmbedBuilder()
            .setTitle(`${contact.contactName}`)
            .setDescription('محادثة الواتساب')
            .setImage('attachment://whatsapp_chat.png')
            .setColor('#25D366');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`whatsapp_send_${contactUserId}`)
                    .setLabel('إرسال رسالة')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('whatsapp_home')
                    .setLabel('العودة للمحادثات')
                    .setStyle(ButtonStyle.Secondary)

            );

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row],
            ephemeral: true
        });
    },

    // عرض مودال إرسال رسالة واتساب
    async showWhatsAppSendModal(interaction, contactUserId) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const contact = await Contact.findOne({
            ownerId: userId,
            ownerCharacterId: characterId,
            contactUserId
        });

        if (!contact) {
            await interaction.reply({
                content: 'جهة الاتصال غير موجودة.',
                ephemeral: true
            });
            return;
        }

        const modal = new ModalBuilder()
            .setCustomId(`whatsapp_send_modal_${contactUserId}`)
            .setTitle(`رسالة إلى ${contact.contactName}`);

        const messageInput = new TextInputBuilder()
            .setCustomId('whatsapp_message')
            .setLabel('الرسالة')
            .setStyle(TextInputStyle.Paragraph)
            .setMinLength(1)
            .setMaxLength(1000)
            .setPlaceholder('اكتب رسالتك هنا...')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(messageInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // معالج الإعدادات
    async handleSettings(interaction, client, customId) {
        const userId = interaction.user.id;

        if (customId === 'settings_home') {
            await this.showSettingsMenu(interaction);
            return;
        }

        if (customId === 'settings_profile') {
            await this.showProfileSettings(interaction);
            return;
        }

        if (customId === 'settings_security') {
            await this.showSecuritySettings(interaction);
            return;
        }

        // معالجة أزرار الحماية
        if (customId.startsWith('security_')) {
            await this.handleSecurityAction(interaction, customId);
            return;
        }
    },

    // عرض قائمة الإعدادات
    async showSettingsMenu(interaction) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const userPhone = await Phone.findOne({ userId, characterId });

        // إنشاء صورة الإعدادات
        const settingsImage = await this.createSettingsImage(interaction.user.username, userPhone?.phoneNumber);
        const attachment = new AttachmentBuilder(settingsImage, { name: 'settings.png' });

        const embed = new EmbedBuilder()
            .setTitle('إعدادات الجوال')
            .setDescription('**اختر الإعداد الذي تريد تعديله:**')
            .setColor('#007AFF')
            .setImage('attachment://settings.png');

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('settings_profile')
                    .setLabel('بياناتك')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('settings_security')
                    .setLabel('إنشاء كلمة سر')
                    .setStyle(ButtonStyle.Danger)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('phone_home')
                    .setLabel('العودة للرئيسية')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row1, row2]
        });
    },

    // عرض إعدادات كلمة المرور
    async showSecuritySettings(interaction) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const userPhone = await Phone.findOne({ userId, characterId });

        // إنشاء صورة إعدادات كلمة المرور
        const securityImage = await this.createPasswordSettingsImage(userPhone);
        const attachment = new AttachmentBuilder(securityImage, { name: 'password.png' });

        const embed = new EmbedBuilder()
            .setTitle('🔒 كلمة المرور')
            .setDescription('إعدادات كلمة مرور الجوال')
            .setImage('attachment://password.png')
            .setColor('#007AFF');

        // الصف الوحيد - كلمة المرور
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('security_set_password')
                    .setLabel(userPhone?.password ? 'تغيير كلمة المرور' : 'إنشاء كلمة مرور')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('security_remove_password')
                    .setLabel('إزالة كلمة المرور')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(!userPhone?.password),
                new ButtonBuilder()
                    .setCustomId('settings_home')
                    .setLabel('العودة')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row1],
            ephemeral: true
        });
    },

    // معالج إجراءات الحماية
    async handleSecurityAction(interaction, customId) {
        const userId = interaction.user.id;

        if (customId === 'security_set_password') {
            await this.showPasswordModal(interaction);
            return;
        }

        if (customId === 'security_remove_password') {
            await this.removePassword(interaction);
            return;
        }

        if (customId === 'security_lock_apps') {
            await interaction.reply({
                content: 'ميزة قفل التطبيقات ستكون متاحة قريباً!',
                ephemeral: true
            });
            return;
        }

        if (customId === 'security_backup') {
            await interaction.reply({
                content: 'ميزة النسخ الاحتياطي ستكون متاحة قريباً!',
                ephemeral: true
            });
            return;
        }
    },

    // عرض نافذة إنشاء كلمة السر (4 أرقام)
    async showPasswordModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('security_password_modal')
            .setTitle('إنشاء كلمة سر للجوال');

        const passwordInput = new TextInputBuilder()
            .setCustomId('password')
            .setLabel('كلمة السر (4 أرقام فقط)')
            .setStyle(TextInputStyle.Short)
            .setMinLength(4)
            .setMaxLength(4)
            .setPlaceholder('مثال: 1234')
            .setRequired(true);

        const confirmPasswordInput = new TextInputBuilder()
            .setCustomId('confirm_password')
            .setLabel('تأكيد كلمة السر')
            .setStyle(TextInputStyle.Short)
            .setMinLength(4)
            .setMaxLength(4)
            .setPlaceholder('أعد إدخال نفس الأرقام')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(passwordInput);
        const secondActionRow = new ActionRowBuilder().addComponents(confirmPasswordInput);

        modal.addComponents(firstActionRow, secondActionRow);

        await interaction.showModal(modal);
    },

    // إزالة كلمة السر
    async removePassword(interaction) {
        try {
            const userId = interaction.user.id;

            await Phone.updateOne(
                { userId },
                { $unset: { password: 1 } }
            );

            await interaction.reply({
                content: 'تم إزالة كلمة السر بنجاح!',
                ephemeral: true
            });

            // تحديث إعدادات الحماية
            setTimeout(async () => {
                await this.showSecuritySettings(interaction);
            }, 1000);

        } catch (error) {
            console.error('خطأ في إزالة كلمة السر:', error);
            await interaction.reply({
                content: 'حدث خطأ أثناء إزالة كلمة السر!',
                ephemeral: true
            });
        }
    },

    // عرض إعدادات الملف الشخصي
    async showProfileSettings(interaction) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const userPhone = await Phone.findOne({ userId, characterId });
        const twitterAccount = await TwitterAccount.findOne({ userId, characterId });

        // إنشاء صورة البيانات الشخصية
        const profileImage = await this.createProfileImage(interaction.user.username, userPhone?.phoneNumber, twitterAccount);
        const attachment = new AttachmentBuilder(profileImage, { name: 'profile.png' });

        const embed = new EmbedBuilder()
            .setTitle('بياناتي الشخصية')
            .setDescription('**معلومات حسابك في الجوال:**')
            .setColor('#007AFF')
            .setImage('attachment://profile.png');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('settings_home')
                    .setLabel('العودة للإعدادات')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row]
        });
    },

    // عرض إعدادات الخلفية
    async showWallpaperSettings(interaction) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        const userPhone = await Phone.findOne({ userId, characterId });

        // إنشاء صورة إعدادات الخلفية
        const wallpaperImage = await this.createWallpaperSettingsImage(interaction.user.username, userPhone);
        const attachment = new AttachmentBuilder(wallpaperImage, { name: 'wallpaper_settings.png' });

        const embed = new EmbedBuilder()
            .setTitle('إعدادات الخلفية')
            .setDescription(`**الخلفية الحالية:** ${userPhone?.wallpaper || 'افتراضية'}\n**اختر خلفية جديدة:**`)
            .setColor('#8b5cf6')
            .setImage('attachment://wallpaper_settings.png');

        // الصف الأول - الخلفيات الافتراضية
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('wallpaper_default')
                    .setLabel('افتراضية')
                    .setStyle(userPhone?.wallpaper === 'default' ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_purple')
                    .setLabel('بنفسجية')
                    .setStyle(userPhone?.wallpaper === 'purple' ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_gradient')
                    .setLabel('تدرج')
                    .setStyle(userPhone?.wallpaper === 'gradient' ? ButtonStyle.Success : ButtonStyle.Secondary)
            );

        // الصف الثاني - خلفيات إضافية
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('wallpaper_dark')
                    .setLabel('داكنة')
                    .setStyle(userPhone?.wallpaper === 'dark' ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_nature')
                    .setLabel('طبيعية')
                    .setStyle(userPhone?.wallpaper === 'nature' ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_space')
                    .setLabel('فضائية')
                    .setStyle(userPhone?.wallpaper === 'space' ? ButtonStyle.Success : ButtonStyle.Secondary)
            );

        // الصف الثالث - خلفيات مخصصة
        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('wallpaper_upload')
                    .setLabel('رفع صورة خلفية')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_custom')
                    .setLabel('خلفية مخصصة')
                    .setStyle(userPhone?.wallpaper === 'custom' ? ButtonStyle.Success : ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('settings_home')
                    .setLabel('العودة للإعدادات')
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.update({
            embeds: [embed],
            files: [attachment],
            components: [row1, row2, row3],
            ephemeral: true
        });
    },

    // معالج تغيير الخلفية
    async handleWallpaperChange(interaction, customId) {
        const userId = interaction.user.id;

        // الحصول على الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        const characterId = activeCharacter ? activeCharacter.characterId : userId;

        let userPhone = await Phone.findOne({ userId, characterId });

        if (!userPhone) {
            await interaction.reply({
                content: 'لم يتم العثور على جوالك. استخدم أمر `جوال` أولاً.',
                ephemeral: true
            });
            return;
        }

        let wallpaperType = 'default';
        let message = '';

        switch (customId) {
            case 'wallpaper_default':
                wallpaperType = 'default';
                message = 'تم تغيير الخلفية إلى الافتراضية!';
                break;
            case 'wallpaper_purple':
                wallpaperType = 'purple';
                message = 'تم تغيير الخلفية إلى البنفسجية!';
                break;
            case 'wallpaper_gradient':
                wallpaperType = 'gradient';
                message = 'تم تغيير الخلفية إلى التدرج!';
                break;
            case 'wallpaper_dark':
                wallpaperType = 'dark';
                message = 'تم تغيير الخلفية إلى الداكنة!';
                break;
            case 'wallpaper_nature':
                wallpaperType = 'nature';
                message = 'تم تغيير الخلفية إلى الطبيعية!';
                break;
            case 'wallpaper_space':
                wallpaperType = 'space';
                message = 'تم تغيير الخلفية إلى الفضائية!';
                break;
            case 'wallpaper_custom':
                // إرسال رسالة خاصة للمستخدم لرفع الصورة
                await this.requestCustomWallpaper(interaction);
                return;
            case 'wallpaper_upload':
                // فتح تعليمات رفع الصورة
                await this.showWallpaperUploadModal(interaction);
                return;
            case 'wallpaper_send_dm':
                // إرسال رسالة خاصة للمستخدم
                await this.sendWallpaperDMInstructions(interaction);
                return;
            case 'wallpaper_url_upload':
                // فتح نافذة رفع بالرابط
                await this.showWallpaperUrlModal(interaction);
                return;
        }

        // تحديث الخلفية في قاعدة البيانات
        userPhone.wallpaper = wallpaperType;
        userPhone.wallpaperType = 'default';
        userPhone.customWallpaperUrl = null;
        await userPhone.save();

        // إعادة عرض إعدادات الخلفية مع رسالة النجاح
        await interaction.reply({
            content: message,
            ephemeral: true
        });

        // تحديث الصفحة
        setTimeout(async () => {
            await this.showWallpaperSettings(interaction);
        }, 1000);
    },

    // طلب خلفية مخصصة
    async requestCustomWallpaper(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📸 خلفية مخصصة')
            .setDescription('**لتغيير خلفية جوالك إلى صورة مخصصة:**\n\n' +
                '1️⃣ أرسل لي الصورة في رسالة خاصة\n' +
                '2️⃣ ستصبح الصورة خلفية جوالك تلقائياً\n\n' +
                '**ملاحظات:**\n' +
                '• يفضل أن تكون الصورة بنسبة 1:2 (عمودية)\n' +
                '• حجم الصورة يجب أن يكون أقل من 8 ميجابايت\n' +
                '• الصيغ المدعومة: PNG, JPG, GIF, WEBP')
            .setColor('#00ff00')
            .setFooter({ text: 'أرسل الصورة في رسالة خاصة فقط' });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('settings_wallpaper')
                    .setLabel('العودة')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            files: [],
            components: [row]
        });
    },

    // عرض تعليمات رفع الخلفية
    async showWallpaperUploadModal(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('رفع خلفية مخصصة')
            .setDescription('**لرفع خلفية مخصصة لجوالك:**\n\n' +
                '1. أرسل لي رسالة خاصة\n' +
                '2. أرفق الصورة التي تريدها كخلفية\n' +
                '3. اكتب `!خلفية` مع الصورة\n' +
                '4. ستصبح الصورة خلفية جوالك فوراً\n\n' +
                '**ملاحظات مهمة:**\n' +
                '• يفضل أن تكون الصورة بنسبة 1:2 (عمودية)\n' +
                '• حجم الصورة يجب أن يكون أقل من 8 ميجابايت\n' +
                '• الصيغ المدعومة: PNG, JPG, GIF, WEBP\n' +
                '• الصورة ستكون مرئية لك فقط')
            .setColor('#8b5cf6')
            .setFooter({ text: 'نظام الجوال الذكي - خلفية مخصصة' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('wallpaper_send_dm')
                    .setLabel('إرسال رسالة خاصة')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('wallpaper_url_upload')
                    .setLabel('استخدام رابط صورة')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('settings_wallpaper')
                    .setLabel('العودة')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row],
            ephemeral: true
        });
    },

    // عرض نافذة رفع الخلفية بالرابط
    async showWallpaperUrlModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('wallpaper_upload_modal')
            .setTitle('رفع خلفية بالرابط');

        const urlInput = new TextInputBuilder()
            .setCustomId('wallpaper_url')
            .setLabel('رابط الصورة')
            .setStyle(TextInputStyle.Short)
            .setMinLength(10)
            .setMaxLength(500)
            .setPlaceholder('https://example.com/image.jpg')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(urlInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // إرسال تعليمات الخلفية في رسالة خاصة
    async sendWallpaperDMInstructions(interaction) {
        try {
            const user = interaction.user;

            const embed = new EmbedBuilder()
                .setTitle('تعليمات رفع خلفية مخصصة')
                .setDescription('**مرحباً! لرفع خلفية مخصصة لجوالك:**\n\n' +
                    '1️⃣ أرفق الصورة التي تريدها كخلفية\n' +
                    '2️⃣ اكتب `!خلفية` في نفس الرسالة\n' +
                    '3️⃣ ستصبح الصورة خلفية جوالك فوراً!\n\n' +
                    '**مثال:**\n' +
                    '• أرفق صورة\n' +
                    '• اكتب: `!خلفية`\n\n' +
                    '**ملاحظات:**\n' +
                    '• الحد الأقصى: 8 ميجابايت\n' +
                    '• الصيغ المدعومة: PNG, JPG, GIF, WEBP\n' +
                    '• يفضل النسبة العمودية للحصول على أفضل نتيجة')
                .setColor('#00ff00')
                .setFooter({ text: 'نظام الجوال الذكي' })
                .setTimestamp();

            await user.send({
                embeds: [embed]
            });

            await interaction.reply({
                content: 'تم إرسال التعليمات في رسالة خاصة! تحقق من رسائلك الخاصة.',
                ephemeral: true
            });

        } catch (error) {
            await interaction.reply({
                content: 'لا يمكن إرسال رسالة خاصة لك. تأكد من أن الرسائل الخاصة مفتوحة.',
                ephemeral: true
            });
        }
    },

    // إنشاء صورة إعدادات الخلفية
    async createWallpaperSettingsImage(username, userPhone) {
        const canvas = createCanvas(400, 600);
        const ctx = canvas.getContext('2d');

        // خلفية متدرجة
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#8b5cf6');
        gradient.addColorStop(1, '#3b82f6');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 600);

        // عنوان
        ctx.font = 'bold 28px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('إعدادات الخلفية', 200, 60);

        // معلومات المستخدم
        ctx.font = 'bold 18px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillText(`مرحباً ${username}`, 200, 100);

        // الخلفية الحالية
        const currentWallpaper = userPhone?.wallpaper || 'default';
        let wallpaperName = 'الافتراضية';

        switch (currentWallpaper) {
            case 'iphone16_purple':
                wallpaperName = 'البنفسجية';
                break;
            case 'iphone16_gradient':
                wallpaperName = 'التدرج';
                break;
            case 'custom':
                wallpaperName = 'مخصصة';
                break;
        }

        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.fillText(`الخلفية الحالية: ${wallpaperName}`, 200, 140);

        // معاينة الخلفيات
        const wallpapers = [
            { name: 'الافتراضية', y: 200 },
            { name: 'البنفسجية', y: 260 },
            { name: 'التدرج', y: 320 },
            { name: 'مخصصة', y: 380 }
        ];

        wallpapers.forEach(wallpaper => {
            // خلفية الخيار
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.roundRect(50, wallpaper.y - 20, 300, 40, 10);
            ctx.fill();

            // النص
            ctx.font = 'bold 16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'center';
            ctx.fillText(wallpaper.name, 200, wallpaper.y);
        });

        // تعليمات
        ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillText('اختر الخلفية التي تريدها من الأزرار أدناه', 200, 480);

        return canvas.toBuffer('image/png');
    },

    // عرض إعدادات الخصوصية
    async showPrivacySettings(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('إعدادات الخصوصية')
            .setDescription('**إعدادات الخصوصية والأمان:**\n\nجميع البيانات محفوظة بشكل آمن\nرقم الجوال مرئي للأعضاء فقط\nالرسائل مشفرة\nالمكالمات آمنة\n\n**ملاحظة:** يمكن للأعضاء الآخرين العثور عليك عبر رقم جوالك فقط.')
            .setColor('#e74c3c')
            .addFields(
                { name: 'الحماية', value: 'البيانات محمية ومشفرة', inline: true },
                { name: 'الرؤية', value: 'مرئي للأعضاء المسجلين فقط', inline: true },
                { name: 'البحث', value: 'يمكن العثور عليك برقم الجوال', inline: true }
            );

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('settings_home')
                    .setLabel('العودة للإعدادات')
                    .setStyle(ButtonStyle.Secondary)

            );

        await interaction.update({
            embeds: [embed],

            components: [row]
        });
    },

    // عرض معلومات حول التطبيق
    async showAboutSettings(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('حول التطبيق')
            .setDescription('**نظام الجوال الذكي**\n\n**الإصدار:** 1.0.0\n**المطور:** فريق التطوير\n**تاريخ الإصدار:** 2024\n\n**الميزات:**\nتويتر متكامل\nنظام المكالمات\nواتساب\nجهات الاتصال\nالإعدادات\n\n**الدعم الفني:**\nللمساعدة والدعم، تواصل مع الإدارة')
            .setColor('#9b59b6')
            .setFooter({ text: 'نظام الجوال الذكي - جميع الحقوق محفوظة' });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('settings_home')
                    .setLabel('العودة للإعدادات')
                    .setStyle(ButtonStyle.Secondary)

            );

        await interaction.update({
            embeds: [embed],

            components: [row]
        });
    },

    // قفل الجوال
    async lockPhone(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('تم قفل الجوال')
            .setDescription('تم قفل جوالك بنجاح.\n\nلفتح الجوال مرة أخرى، استخدم الأمر:\n`جوال` أو `phone`')
            .setColor('#2c3e50')
            .setTimestamp();

        await interaction.update({
            embeds: [embed],

            components: []
        });
    },

    // معالج المكالمات
    async handleCalls(interaction, client, customId) {
        const userId = interaction.user.id;

        if (customId.startsWith('call_answer_')) {
            const callId = customId.replace('call_answer_', '');
            await this.answerCall(interaction, callId);
            return;
        }

        if (customId.startsWith('call_decline_')) {
            const callId = customId.replace('call_decline_', '');
            await this.declineCall(interaction, callId);
            return;
        }

        if (customId.startsWith('call_end_')) {
            const callId = customId.replace('call_end_', '');
            await this.endCall(interaction, callId);
            return;
        }
    },

    // الرد على المكالمة
    async answerCall(interaction, callId) {
        const call = await Call.findOne({ callId });

        if (!call || call.status !== 'calling') {
            await interaction.reply({
                content: 'المكالمة غير متاحة أو انتهت.',
                ephemeral: true
            });
            return;
        }

        // تحديث حالة المكالمة
        call.status = 'answered';
        await call.save();

        // عرض شاشة المكالمة النشطة
        const embed = new EmbedBuilder()
            .setTitle('مكالمة نشطة')
            .setDescription(`متصل مع **${call.callerName}**\n\nالمكالمة جارية...`)
            .setColor('#27ae60')
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`call_end_${callId}`)
                    .setLabel('إنهاء المكالمة')
                    .setStyle(ButtonStyle.Danger)

            );

        await interaction.update({
            embeds: [embed],
            components: [row]
        });

        // إشعار المتصل بالرد
        try {
            const callerUser = await interaction.client.users.fetch(call.callerId);
            if (callerUser) {
                const answerEmbed = new EmbedBuilder()
                    .setTitle('تم الرد على المكالمة')
                    .setDescription(`**${call.receiverName}** رد على المكالمة\n\nالمكالمة نشطة الآن`)
                    .setColor('#27ae60');

                const answerRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`call_end_${callId}`)
                            .setLabel('إنهاء المكالمة')
                            .setStyle(ButtonStyle.Danger)

                    );

                await callerUser.send({
                    embeds: [answerEmbed],
                    components: [answerRow]
                });
            }
        } catch (error) {
            console.log('لا يمكن إرسال إشعار الرد للمتصل');
        }
    },

    // رفض المكالمة
    async declineCall(interaction, callId) {
        const call = await Call.findOne({ callId });

        if (!call) {
            await interaction.reply({
                content: 'المكالمة غير موجودة.',
                ephemeral: true
            });
            return;
        }

        // تحديث حالة المكالمة
        call.status = 'declined';
        call.endTime = new Date();
        await call.save();

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setTitle('تم رفض المكالمة')
                .setDescription(`تم رفض مكالمة **${call.callerName}**`)
                .setColor('#e74c3c')],
            components: []
        });

        // إشعار المتصل بالرفض
        try {
            const callerUser = await interaction.client.users.fetch(call.callerId);
            if (callerUser) {
                await callerUser.send({
                    embeds: [new EmbedBuilder()
                        .setTitle('تم رفض المكالمة')
                        .setDescription(`**${call.receiverName}** رفض المكالمة`)
                        .setColor('#e74c3c')]
                });
            }
        } catch (error) {
            console.log('لا يمكن إرسال إشعار الرفض للمتصل');
        }
    },

    // إنهاء المكالمة
    async endCall(interaction, callId) {
        const call = await Call.findOne({ callId });

        if (!call) {
            await interaction.reply({
                content: 'المكالمة غير موجودة.',
                ephemeral: true
            });
            return;
        }

        // حساب مدة المكالمة
        const duration = Math.floor((new Date() - call.startTime) / 1000);

        // تحديث حالة المكالمة
        call.status = 'ended';
        call.endTime = new Date();
        call.duration = duration;
        await call.save();

        const durationText = duration > 60
            ? `${Math.floor(duration / 60)} دقيقة و ${duration % 60} ثانية`
            : `${duration} ثانية`;

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setTitle('انتهت المكالمة')
                .setDescription(`انتهت المكالمة مع **${call.status === 'answered' ? (interaction.user.id === call.callerId ? call.receiverName : call.callerName) : 'المستخدم'}**\n\nالمدة: ${durationText}`)
                .setColor('#95a5a6')],
            components: []
        });

        // إشعار الطرف الآخر
        try {
            const otherUserId = interaction.user.id === call.callerId ? call.receiverId : call.callerId;
            const otherUser = await interaction.client.users.fetch(otherUserId);
            if (otherUser) {
                await otherUser.send({
                    embeds: [new EmbedBuilder()
                        .setTitle('انتهت المكالمة')
                        .setDescription(`انتهت المكالمة\n\nالمدة: ${durationText}`)
                        .setColor('#95a5a6')]
                });
            }
        } catch (error) {
            console.log('لا يمكن إرسال إشعار انتهاء المكالمة');
        }
    },

    // دالة مساعدة لحساب الوقت المنقضي
    getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'الآن';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
        return `${Math.floor(diffInSeconds / 86400)} يوم`;
    },

    // معالج المودالات
    async handlePhoneModal(interaction, client) {
        try {
            const customId = interaction.customId;
            const userId = interaction.user.id;

            // مودال التسجيل في تويتر
            if (customId === 'twitter_signup_modal') {
                const username = interaction.fields.getTextInputValue('twitter_username');
                const displayName = interaction.fields.getTextInputValue('twitter_display_name');
                const bio = interaction.fields.getTextInputValue('twitter_bio') || '';

                // التحقق من عدم وجود اسم المستخدم
                const existingAccount = await TwitterAccount.findOne({ username: username.toLowerCase() });
                if (existingAccount) {
                    await interaction.reply({
                        content: 'اسم المستخدم موجود بالفعل. اختر اسماً آخر.',
                        ephemeral: true
                    });
                    return;
                }

                // إنشاء الحساب
                const newAccount = new TwitterAccount({
                    userId,
                    username: username.toLowerCase(),
                    displayName,
                    bio
                });

                await newAccount.save();

                await interaction.reply({
                    content: `تم إنشاء حسابك في تويتر بنجاح!\n**اسم المستخدم:** @${username}\n**الاسم المعروض:** ${displayName}`,
                    ephemeral: true
                });

                // عرض تغذية تويتر
                setTimeout(async () => {
                    await this.showTwitterFeed(interaction, newAccount);
                }, 2000);

                return;
            }

            // مودال التغريدة
            if (customId === 'twitter_tweet_modal') {
                const content = interaction.fields.getTextInputValue('tweet_content');
                const twitterAccount = await TwitterAccount.findOne({ userId });

                if (!twitterAccount) {
                    await interaction.reply({
                        content: 'لا يمكن العثور على حسابك في تويتر.',
                        ephemeral: true
                    });
                    return;
                }

                // الحصول على معلومات الشخصية
                const activeCharacter = await require('../models/activeCh').findOne({ userId });
                let characterId = userId;

                if (activeCharacter) {
                    characterId = activeCharacter.characterId;
                }

                // إنشاء التغريدة
                const tweetId = Date.now().toString();
                const newTweet = new Tweet({
                    tweetId,
                    userId,
                    characterId,
                    username: twitterAccount.username,
                    displayName: twitterAccount.displayName,
                    profileImage: twitterAccount.profileImage,
                    content
                });

                await newTweet.save();

                await interaction.reply({
                    content: `✅ تم نشر تغريدتك بنجاح!\n\n**@${twitterAccount.username}** (${twitterAccount.displayName})\n${content}`,
                    ephemeral: true
                });

                // تحديث التغذية
                setTimeout(async () => {
                    try {
                        // إنشاء تغذية جديدة
                        const tweets = await Tweet.find({})
                            .populate('userId', 'username')
                            .sort({ createdAt: -1 })
                            .limit(20);

                        const twitterImage = await this.createTwitterFeedImageWithNavigation(twitterAccount, tweets, 0);
                        const attachment = new AttachmentBuilder(twitterImage, { name: 'twitter_feed.png' });

                        const embed = new EmbedBuilder()
                            .setTitle('تويتر')
                            .setDescription(`التغريدة 1 من ${tweets.length}`)
                            .setImage('attachment://twitter_feed.png')
                            .setColor('#1DA1F2');

                        await interaction.editReply({
                            content: 'تم نشر التغريدة! إليك التغذية المحدثة:',
                            embeds: [embed],
                            files: [attachment],
                            components: []
                        });
                    } catch (error) {
                        console.log('خطأ في تحديث التغذية:', error);
                    }
                }, 2000);

                return;
            }

            // مودال إضافة جهة اتصال
            if (customId === 'contacts_add_modal') {
                const phoneNumber = interaction.fields.getTextInputValue('contact_phone');
                const contactName = interaction.fields.getTextInputValue('contact_name');

                // الحصول على الشخصية النشطة للمالك
                const activeCharacter = await require('../models/activeCh').findOne({ userId });
                const ownerCharacterId = activeCharacter ? activeCharacter.characterId : userId;

                // البحث عن المستخدم بالرقم
                const targetPhone = await Phone.findOne({ phoneNumber });
                if (!targetPhone) {
                    await interaction.reply({
                        content: 'لم يتم العثور على مستخدم بهذا الرقم.',
                        ephemeral: true
                    });
                    return;
                }

                // التحقق من عدم إضافة نفسه
                if (targetPhone.userId === userId && targetPhone.characterId === ownerCharacterId) {
                    await interaction.reply({
                        content: 'لا يمكنك إضافة رقمك الخاص.',
                        ephemeral: true
                    });
                    return;
                }

                // التحقق من عدم وجود جهة الاتصال مسبقاً
                const existingContact = await Contact.findOne({
                    ownerId: userId,
                    ownerCharacterId: ownerCharacterId,
                    contactUserId: targetPhone.userId,
                    contactCharacterId: targetPhone.characterId
                });

                if (existingContact) {
                    await interaction.reply({
                        content: 'جهة الاتصال موجودة بالفعل.',
                        ephemeral: true
                    });
                    return;
                }

                // إضافة جهة الاتصال
                const newContact = new Contact({
                    ownerId: userId,
                    ownerCharacterId: ownerCharacterId,
                    contactUserId: targetPhone.userId,
                    contactCharacterId: targetPhone.characterId,
                    contactName,
                    contactPhone: phoneNumber
                });

                await newContact.save();

                await interaction.reply({
                    content: `✅ تم إضافة **${contactName}** إلى جهات الاتصال بنجاح!`,
                    ephemeral: true
                });

                // تحديث قائمة جهات الاتصال
                setTimeout(async () => {
                    await this.showContactsList(interaction);
                }, 2000);

                return;
            }

            // مودال تعديل الملف الشخصي
            if (customId === 'twitter_edit_profile_modal') {
                const displayName = interaction.fields.getTextInputValue('edit_display_name');
                const bio = interaction.fields.getTextInputValue('edit_bio') || '';

                const twitterAccount = await TwitterAccount.findOne({ userId });
                if (!twitterAccount) {
                    await interaction.reply({
                        content: 'لا يمكن العثور على حسابك في تويتر.',
                        ephemeral: true
                    });
                    return;
                }

                // تحديث البيانات
                twitterAccount.displayName = displayName;
                twitterAccount.bio = bio;
                await twitterAccount.save();

                // تحديث جميع التغريدات بالاسم الجديد
                await Tweet.updateMany(
                    { userId },
                    { displayName }
                );

                await interaction.reply({
                    content: `✅ تم تحديث ملفك الشخصي بنجاح!\n**الاسم الجديد:** ${displayName}\n**النبذة:** ${bio || 'لا توجد نبذة'}`,
                    ephemeral: true
                });

                // عرض الملف الشخصي المحدث
                setTimeout(async () => {
                    await this.showTwitterProfile(interaction);
                }, 2000);

                return;
            }

            // مودال إرسال رسالة واتساب
            if (customId.startsWith('whatsapp_send_modal_')) {
                const contactUserId = customId.replace('whatsapp_send_modal_', '');
                const messageContent = interaction.fields.getTextInputValue('whatsapp_message');

                // الحصول على الشخصية النشطة
                const activeCharacter = await require('../models/activeCh').findOne({ userId });
                const characterId = activeCharacter ? activeCharacter.characterId : userId;

                // جلب معلومات المرسل والمستقبل
                const senderContact = await Contact.findOne({
                    ownerId: userId,
                    ownerCharacterId: characterId,
                    contactUserId
                });

                if (!senderContact) {
                    await interaction.reply({
                        content: 'جهة الاتصال غير موجودة.',
                        ephemeral: true
                    });
                    return;
                }

                // الحصول على معلومات الشخصيات
                const activeCh = require('../models/activeCh');
                const senderActiveChar = await activeCh.findOne({ userId });
                const receiverActiveChar = await activeCh.findOne({ userId: contactUserId });

                // إنشاء الرسالة
                const messageId = Date.now().toString();
                const newMessage = new WhatsAppMessage({
                    messageId,
                    senderId: userId,
                    senderCharacterId: senderActiveChar?.characterId || userId,
                    receiverId: contactUserId,
                    receiverCharacterId: receiverActiveChar?.characterId || contactUserId,
                    senderName: interaction.user.username,
                    receiverName: senderContact.contactName,
                    content: messageContent
                });

                await newMessage.save();

                await interaction.reply({
                    content: `✅ تم إرسال الرسالة إلى **${senderContact.contactName}** بنجاح!`,
                    ephemeral: true
                });

                // إرسال إشعار للمستقبل
                try {
                    const receiverUser = await interaction.client.users.fetch(contactUserId);
                    if (receiverUser) {
                        const notificationEmbed = new EmbedBuilder()
                            .setTitle('رسالة واتساب جديدة')
                            .setDescription(`**${interaction.user.username}** أرسل لك رسالة:\n\n"${messageContent}"`)
                            .setColor('#25D366')
                            .setTimestamp();

                        const notificationRow = new ActionRowBuilder()
                            .addComponents(
                                new ButtonBuilder()
                                    .setCustomId(`whatsapp_chat_${userId}`)
                                    .setLabel('عرض المحادثة')
                                    .setStyle(ButtonStyle.Primary)

                            );

                        await receiverUser.send({
                            embeds: [notificationEmbed],
                            components: [notificationRow]
                        });
                    }
                } catch (error) {
                    console.log('لا يمكن إرسال إشعار الواتساب للمستقبل');
                }

                // تحديث المحادثة
                setTimeout(async () => {
                    await this.showWhatsAppChat(interaction, contactUserId);
                }, 2000);

                return;
            }

            // مودال إرسال صورة في الواتساب
            if (customId.startsWith('whatsapp_image_modal_')) {
                const contactUserId = customId.replace('whatsapp_image_modal_', '');
                const imageUrl = interaction.fields.getTextInputValue('image_url');
                const imageMessage = interaction.fields.getTextInputValue('image_message') || '';

                try {
                    // التحقق من صحة الرابط
                    const url = new URL(imageUrl);

                    // التحقق من أن الرابط يشير إلى صورة
                    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
                    const hasImageExtension = imageExtensions.some(ext =>
                        url.pathname.toLowerCase().endsWith(ext)
                    );

                    if (!hasImageExtension) {
                        await interaction.reply({
                            content: 'الرابط يجب أن يشير إلى صورة صالحة (jpg, png, gif, webp)',
                            ephemeral: true
                        });
                        return;
                    }

                    // إنشاء رسالة الصورة
                    const messageContent = imageMessage ? `${imageMessage}\n[صورة: ${imageUrl}]` : `[صورة: ${imageUrl}]`;

                    const newMessage = new WhatsAppMessage({
                        senderId: userId,
                        receiverId: contactUserId,
                        content: messageContent,
                        hasImage: true,
                        imageUrl: imageUrl,
                        createdAt: new Date()
                    });

                    await newMessage.save();

                    await interaction.reply({
                        content: 'تم إرسال الصورة بنجاح!',
                        ephemeral: true
                    });

                    // تحديث المحادثة
                    setTimeout(async () => {
                        await this.showWhatsAppChatEnhanced(interaction, contactUserId);
                    }, 1000);

                } catch (error) {
                    await interaction.reply({
                        content: 'رابط الصورة غير صحيح! تأكد من أن الرابط صالح ويشير إلى صورة.',
                        ephemeral: true
                    });
                }

                return;
            }

            // مودال الرد على التغريدة
            if (customId.startsWith('twitter_reply_modal_')) {
                const parts = customId.replace('twitter_reply_modal_', '').split('_');
                const tweetId = parts[0];
                const currentIndex = parseInt(parts[1]);
                const replyContent = interaction.fields.getTextInputValue('reply_content');

                try {
                    // العثور على التغريدة الأصلية
                    const originalTweet = await Tweet.findById(tweetId);
                    if (!originalTweet) {
                        await interaction.reply({
                            content: 'التغريدة غير موجودة!',
                            ephemeral: true
                        });
                        return;
                    }

                    // الحصول على معلومات الشخصية
                    const activeCharacter = await require('../models/activeCh').findOne({ userId });
                    let characterId = userId;

                    if (activeCharacter) {
                        characterId = activeCharacter.characterId;
                    }

                    // إضافة الرد للتغريدة الأصلية
                    const newReply = {
                        userId,
                        username: characterId,
                        displayName: characterId,
                        content: replyContent,
                        createdAt: new Date()
                    };

                    originalTweet.replies.push(newReply);
                    await originalTweet.save();

                    if (!interaction.replied && !interaction.deferred) {
                        await interaction.reply({
                            content: 'تم إضافة الرد بنجاح!',
                            ephemeral: true
                        });
                    }

                } catch (error) {
                    console.error('خطأ في إضافة الرد:', error);
                    if (!interaction.replied && !interaction.deferred) {
                        try {
                            await interaction.reply({
                                content: 'حدث خطأ أثناء إضافة الرد!',
                                ephemeral: true
                            });
                        } catch (replyError) {
                            console.error('خطأ في الرد على interaction:', replyError);
                        }
                    }
                }

                return;
            }

            // مودال التغريدة مع صورة
            if (customId === 'twitter_tweet_image_modal') {
                const content = interaction.fields.getTextInputValue('tweet_content');
                const twitterAccount = await TwitterAccount.findOne({ userId });

                if (!twitterAccount) {
                    await interaction.reply({
                        content: 'يجب إنشاء حساب تويتر أولاً!',
                        ephemeral: true
                    });
                    return;
                }

                // حفظ محتوى التغريدة مؤقتاً
                global.pendingTweetWithImage = global.pendingTweetWithImage || {};
                global.pendingTweetWithImage[userId] = {
                    content: content,
                    twitterAccount: twitterAccount
                };

                // طلب الصورة في الخاص
                try {
                    const user = await interaction.client.users.fetch(userId);
                    await user.send({
                        content: `📸 **تغريدة مع صورة**\n\n**محتوى التغريدة:** ${content}\n\n**الآن أرسل الصورة التي تريد إرفاقها مع التغريدة في هذه الرسالة الخاصة.**\n\n*ملاحظة: أرسل الصورة كمرفق وليس كرابط*`
                    });

                    await interaction.reply({
                        content: '✅ تم حفظ محتوى التغريدة!\n📩 تم إرسال رسالة خاصة لك لإرفاق الصورة.',
                        ephemeral: true
                    });
                } catch (error) {
                    console.error('خطأ في إرسال رسالة خاصة:', error);
                    await interaction.reply({
                        content: '❌ لا يمكن إرسال رسالة خاصة لك. تأكد من أن الرسائل الخاصة مفتوحة.',
                        ephemeral: true
                    });
                }

                return;
            }

            // مودال البحث في تويتر
            if (customId === 'twitter_search_modal') {
                const searchQuery = interaction.fields.getTextInputValue('search_query');
                const twitterAccount = await TwitterAccount.findOne({ userId });

                if (!twitterAccount) {
                    await interaction.reply({
                        content: 'يجب إنشاء حساب تويتر أولاً!',
                        ephemeral: true
                    });
                    return;
                }

                try {
                    // البحث الجزئي في التغريدات
                    const searchResults = await Tweet.find({
                        content: { $regex: searchQuery, $options: 'i' } // البحث الجزئي غير حساس للحالة
                    })
                    .populate('userId', 'username')
                    .sort({ createdAt: -1 })
                    .limit(10);

                    if (searchResults.length === 0) {
                        await interaction.reply({
                            content: `❌ لم يتم العثور على تغريدات تحتوي على: "${searchQuery}"`,
                            ephemeral: true
                        });
                        return;
                    }

                    // إنشاء صورة نتائج البحث
                    const searchImage = await this.createTwitterSearchResultsImage(searchResults, searchQuery);
                    const attachment = new AttachmentBuilder(searchImage, { name: 'search_results.png' });

                    const embed = new EmbedBuilder()
                        .setTitle(`🔍 نتائج البحث: "${searchQuery}"`)
                        .setDescription(`تم العثور على ${searchResults.length} تغريدة`)
                        .setImage('attachment://search_results.png')
                        .setColor('#1DA1F2');

                    const backRow = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId('twitter_home')
                                .setLabel('العودة لتويتر')
                                .setStyle(ButtonStyle.Primary)
                        );

                    await interaction.reply({
                        embeds: [embed],
                        files: [attachment],
                        components: [backRow],
                        ephemeral: true
                    });

                } catch (error) {
                    console.error('خطأ في البحث:', error);
                    await interaction.reply({
                        content: 'حدث خطأ أثناء البحث!',
                        ephemeral: true
                    });
                }

                return;
            }

            // مودال إنشاء كلمة سر
            if (customId === 'security_password_modal') {
                const password = interaction.fields.getTextInputValue('password');
                const confirmPassword = interaction.fields.getTextInputValue('confirm_password');

                // التحقق من أن كلمة السر أرقام فقط
                if (!/^\d{4}$/.test(password)) {
                    await interaction.reply({
                        content: 'كلمة السر يجب أن تكون 4 أرقام فقط! مثال: 1234',
                        ephemeral: true
                    });
                    return;
                }

                // التحقق من تطابق كلمات السر
                if (password !== confirmPassword) {
                    await interaction.reply({
                        content: 'كلمات السر غير متطابقة! حاول مرة أخرى.',
                        ephemeral: true
                    });
                    return;
                }

                try {
                    // حفظ كلمة السر (يجب تشفيرها في التطبيق الحقيقي)
                    await Phone.updateOne(
                        { userId },
                        { password: password }
                    );

                    await interaction.reply({
                        content: 'تم إنشاء كلمة السر بنجاح! جوالك الآن محمي.',
                        ephemeral: true
                    });

                    // تحديث إعدادات الحماية
                    setTimeout(async () => {
                        await this.showSecuritySettings(interaction);
                    }, 1000);

                } catch (error) {
                    console.error('خطأ في حفظ كلمة السر:', error);
                    await interaction.reply({
                        content: 'حدث خطأ أثناء حفظ كلمة السر!',
                        ephemeral: true
                    });
                }

                return;
            }

            // مودال التحقق من كلمة سر الجوال
            if (customId.startsWith('phone_password_prompt_')) {
                const phoneId = customId.replace('phone_password_prompt_', '');
                const enteredPassword = interaction.fields.getTextInputValue('phone_password');

                try {
                    const userPhone = await Phone.findById(phoneId);

                    if (!userPhone) {
                        await interaction.reply({
                            content: 'الجوال غير موجود!',
                            ephemeral: true
                        });
                        return;
                    }

                    // التحقق من كلمة السر
                    if (userPhone.password !== enteredPassword) {
                        await interaction.reply({
                            content: 'كلمة السر غير صحيحة! حاول مرة أخرى.',
                            ephemeral: true
                        });
                        return;
                    }

                    // كلمة السر صحيحة - فتح الجوال
                    // الحصول على اسم الشخصية
                    const activeCharacter = await require('../models/activeCh').findOne({ userId });
                    let characterName = interaction.user.username;

                    if (activeCharacter) {
                        const character = await require('../models/Character').findOne({
                            userId,
                            characterName: activeCharacter.characterId
                        });
                        if (character) {
                            characterName = character.characterName;
                        }
                    }

                    const phoneImage = await this.createCustomPhoneImage(userId, userPhone.phoneNumber);
                    const attachment = new AttachmentBuilder(phoneImage, { name: 'iphone.png' });

                    const welcomeMessage = `**Welcome ${characterName}!**\n\n**رقم جوالك:** ${userPhone.phoneNumber}`;

                    const selectMenu = new StringSelectMenuBuilder()
                        .setCustomId(`phone_menu_${userId}`)
                        .setPlaceholder('اختر ما تريد فعله...')
                        .addOptions([
                            {
                                label: 'فتح iPhone',
                                description: 'فتح الشاشة الرئيسية للجوال',
                                value: 'open_phone'
                            }
                        ]);

                    const row = new ActionRowBuilder().addComponents(selectMenu);

                    const embed = new EmbedBuilder()
                        .setTitle('جوالك الذكي')
                        .setDescription(welcomeMessage)
                        .setColor('#1DA1F2')
                        .setImage('attachment://iphone.png')
                        .setFooter({ text: 'نظام الجوالات الذكي' })
                        .setTimestamp();

                    await interaction.reply({
                        embeds: [embed],
                        files: [attachment],
                        components: [row],
                        ephemeral: true
                    });

                } catch (error) {
                    console.error('خطأ في التحقق من كلمة السر:', error);
                    await interaction.reply({
                        content: 'حدث خطأ أثناء التحقق من كلمة السر!',
                        ephemeral: true
                    });
                }

                return;
            }

            // مودال رفع خلفية مخصصة بالرابط
            if (customId === 'wallpaper_upload_modal') {
                const wallpaperUrl = interaction.fields.getTextInputValue('wallpaper_url');

                try {
                    // التحقق من صحة الرابط
                    const url = new URL(wallpaperUrl);

                    // التحقق من أن الرابط يشير إلى صورة
                    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
                    const hasImageExtension = imageExtensions.some(ext =>
                        url.pathname.toLowerCase().endsWith(ext)
                    );

                    if (!hasImageExtension) {
                        await interaction.reply({
                            content: 'الرابط يجب أن يشير إلى صورة صالحة (jpg, png, gif, webp)',
                            ephemeral: true
                        });
                        return;
                    }

                    // البحث عن جوال المستخدم
                    // الحصول على الشخصية النشطة
                    const activeCharacter = await require('../models/activeCh').findOne({ userId });
                    const characterId = activeCharacter ? activeCharacter.characterId : userId;

                    let userPhone = await Phone.findOne({ userId, characterId });

                    if (!userPhone) {
                        await interaction.reply({
                            content: 'لم يتم العثور على جوالك!',
                            ephemeral: true
                        });
                        return;
                    }

                    // حفظ رابط الصورة كخلفية مخصصة
                    userPhone.wallpaper = 'custom';
                    userPhone.wallpaperType = 'image';
                    userPhone.customWallpaperUrl = wallpaperUrl;
                    await userPhone.save();

                    await interaction.reply({
                        content: 'تم تطبيق الخلفية المخصصة بنجاح! استخدم أمر `جوال` لرؤية التغيير.',
                        ephemeral: true
                    });

                    // تحديث إعدادات الخلفية
                    setTimeout(async () => {
                        await this.showWallpaperSettings(interaction);
                    }, 2000);

                } catch (error) {
                    await interaction.reply({
                        content: 'رابط الصورة غير صحيح! تأكد من أن الرابط صالح ويشير إلى صورة.',
                        ephemeral: true
                    });
                }

                return;
            }

        } catch (error) {
            console.error('خطأ في معالج مودالات الجوال:', error);
            if (!interaction.replied) {
                await interaction.reply({
                    content: 'حدث خطأ أثناء معالجة البيانات.',
                    ephemeral: true
                });
            }
        }
    },

    // دالة لإنشاء رقم جوال عشوائي
    generatePhoneNumber() {
        const prefix = '+966';
        const number = Math.floor(Math.random() * 900000000) + 100000000;
        return `${prefix}${number}`;
    },

    // دالة إنشاء شاشة عرض بسيطة وجميلة
    async createCustomPhoneImage(userId, phoneNumber) {
        // الحصول على اسم الشخصية النشطة
        const activeCharacter = await require('../models/activeCh').findOne({ userId });
        let characterName = 'المستخدم';

        if (activeCharacter) {
            const Character = require('../models/Character');
            const character = await Character.findOne({
                userId,
                characterName: activeCharacter.characterId
            });
            if (character) {
                characterName = character.characterName;
            }
        }

        // إنشاء canvas بحجم شاشة عرض
        const canvas = createCanvas(400, 700);
        const ctx = canvas.getContext('2d');

        // خلفية شاشة العرض - ألوان داكنة ومريحة للعين
        const bgGradient = ctx.createLinearGradient(0, 0, 0, 700);
        bgGradient.addColorStop(0, '#2C3E50');
        bgGradient.addColorStop(0.5, '#34495E');
        bgGradient.addColorStop(1, '#1A252F');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 700);

        // شريط الحالة البسيط
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 16px Arial';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 2;

        // الوقت في المنتصف (12 ساعة)
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        ctx.textAlign = 'center';
        ctx.fillText(timeString, 200, 40);

        // شركة الاتصال
        ctx.textAlign = 'left';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('STC', 30, 40);

        // البطارية
        ctx.textAlign = 'right';
        ctx.fillText('100%', 370, 40);

        // إزالة الظل
        ctx.shadowBlur = 0;

        // التاريخ واليوم
        const dateString = now.toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // التاريخ واليوم في الأعلى
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'center';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
        ctx.shadowBlur = 2;
        ctx.fillText(dateString, 200, 120);

        // الوقت الكبير (12 ساعة)
        const bigTimeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 64px Arial';
        ctx.shadowBlur = 4;
        ctx.fillText(bigTimeString, 200, 200);

        // اسم الشخصية
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 28px Arial';
        ctx.shadowBlur = 3;
        ctx.fillText(`مرحباً ${characterName}`, 200, 260);

        // رقم الجوال
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = 'bold 18px Arial';
        ctx.shadowBlur = 2;
        ctx.fillText(`📱 ${phoneNumber}`, 200, 300);

        // إزالة الظل
        ctx.shadowBlur = 0;

        // أيقونات التطبيقات البسيطة والجميلة
        const apps = [
            { name: 'تويتر', x: 100, y: 380, color: '#1DA1F2', icon: '🐦' },
            { name: 'واتساب', x: 200, y: 380, color: '#25D366', icon: '💬' },
            { name: 'جهات الاتصال', x: 300, y: 380, color: '#FF6B35', icon: '👥' },
            { name: 'الإعدادات', x: 100, y: 480, color: '#8E8E93', icon: '⚙️' },
            { name: 'الكاميرا', x: 200, y: 480, color: '#34C759', icon: '📷' },
            { name: 'الملفات', x: 300, y: 480, color: '#5856D6', icon: '📁' }
        ];

        apps.forEach(app => {
            // ظل بسيط للأيقونة
            ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.beginPath();
            ctx.roundRect(app.x - 27, app.y - 27, 54, 54, 15);
            ctx.fill();

            // خلفية الأيقونة مع تدرج بسيط
            const appGradient = ctx.createLinearGradient(app.x - 25, app.y - 25, app.x + 25, app.y + 25);
            appGradient.addColorStop(0, this.lightenColor(app.color, 15));
            appGradient.addColorStop(1, app.color);
            ctx.fillStyle = appGradient;
            ctx.beginPath();
            ctx.roundRect(app.x - 25, app.y - 25, 50, 50, 12);
            ctx.fill();

            // أيقونة التطبيق
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 26px Arial';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 1;
            ctx.fillText(app.icon, app.x, app.y + 8);

            // اسم التطبيق
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.font = 'bold 13px Arial';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
            ctx.shadowBlur = 1;
            ctx.fillText(app.name, app.x, app.y + 42);

            ctx.shadowBlur = 0;
        });

        // رسالة ترحيب في الأسفل
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 1;
        ctx.fillText('اضغط على التطبيقات للوصول إليها', 200, 580);

        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.font = '14px Arial';
        ctx.shadowBlur = 1;
        ctx.fillText('مرحباً بك في جوالك الذكي', 200, 620);

        ctx.shadowBlur = 0;

        return canvas.toBuffer('image/png');
    },

    // دالة لتغميق الألوان
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    },

    // دالة لتفتيح الألوان
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    },

    // دالة لإنشاء صورة الشاشة الرئيسية المحسنة
    async createCustomHomeImageEnhanced(username, userPhone) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية الجوال حسب الخلفية المختارة
        await this.drawWallpaper(ctx, userPhone?.wallpaper || 'default');

        // شريط الحالة العلوي
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';

        // الوقت (12 ساعة)
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        ctx.textAlign = 'center';
        ctx.fillText(timeString, 200, 30);

        // شركة الاتصال
        ctx.textAlign = 'left';
        ctx.fillText('STC', 20, 30);

        // البطارية
        ctx.textAlign = 'right';
        ctx.fillText('100%', 380, 30);

        // التاريخ بالإنجليزية
        const dateString = now.toLocaleDateString('en-US', {
            weekday: 'long',
            month: 'long',
            day: 'numeric'
        });
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(dateString, 200, 80);

        // اسم المستخدم
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 20px Arial';
        ctx.fillText(`${username}`, 200, 120);

        // رقم الجوال
        ctx.fillStyle = 'rgba(255,255,255,0.8)';
        ctx.font = '14px Arial';
        ctx.fillText(`${userPhone?.phoneNumber || 'غير محدد'}`, 200, 145);

        // أيقونات التطبيقات مع تصميم محسن
        const apps = [
            { name: 'تويتر', x: 80, y: 220, color: '#1DA1F2', icon: 'T' },
            { name: 'واتساب', x: 200, y: 220, color: '#25D366', icon: 'W' },
            { name: 'جهات الاتصال', x: 320, y: 220, color: '#FF6B35', icon: 'C' },
            { name: 'المكالمات', x: 80, y: 350, color: '#34C759', icon: 'P' },
            { name: 'الإعدادات', x: 200, y: 350, color: '#8E8E93', icon: 'S' },
            { name: 'الكاميرا', x: 320, y: 350, color: '#FF3B30', icon: 'M' }
        ];

        apps.forEach(app => {
            // ظل التطبيق
            ctx.fillStyle = 'rgba(0,0,0,0.3)';
            ctx.fillRect(app.x - 28, app.y - 26, 56, 56);

            // خلفية التطبيق مع تدرج
            const appGradient = ctx.createLinearGradient(app.x - 30, app.y - 30, app.x + 30, app.y + 30);
            appGradient.addColorStop(0, app.color);
            appGradient.addColorStop(1, this.darkenColor(app.color, 20));
            ctx.fillStyle = appGradient;
            ctx.fillRect(app.x - 30, app.y - 30, 60, 60);

            // أيقونة التطبيق
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(app.icon, app.x, app.y + 5);

            // اسم التطبيق
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 10px Arial';
            ctx.fillText(app.name, app.x, app.y + 45);
        });

        // رسالة في الأسفل
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.font = '12px Arial';
        ctx.fillText('اختر التطبيق من القائمة أدناه', 200, 500);

        return canvas.toBuffer('image/png');
    },

    // دالة رسم الخلفية
    async drawWallpaper(ctx, wallpaperType) {
        const width = 400;
        const height = 800;

        switch (wallpaperType) {
            case 'purple':
                const purpleGradient = ctx.createLinearGradient(0, 0, 0, height);
                purpleGradient.addColorStop(0, '#667eea');
                purpleGradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = purpleGradient;
                break;
            case 'gradient':
                const gradient = ctx.createLinearGradient(0, 0, 0, height);
                gradient.addColorStop(0, '#ff7e5f');
                gradient.addColorStop(1, '#feb47b');
                ctx.fillStyle = gradient;
                break;
            case 'dark':
                const darkGradient = ctx.createLinearGradient(0, 0, 0, height);
                darkGradient.addColorStop(0, '#2c3e50');
                darkGradient.addColorStop(1, '#000000');
                ctx.fillStyle = darkGradient;
                break;
            case 'nature':
                const natureGradient = ctx.createLinearGradient(0, 0, 0, height);
                natureGradient.addColorStop(0, '#56ab2f');
                natureGradient.addColorStop(1, '#a8e6cf');
                ctx.fillStyle = natureGradient;
                break;
            case 'space':
                const spaceGradient = ctx.createLinearGradient(0, 0, 0, height);
                spaceGradient.addColorStop(0, '#000428');
                spaceGradient.addColorStop(1, '#004e92');
                ctx.fillStyle = spaceGradient;
                break;
            default: // default
                const defaultGradient = ctx.createLinearGradient(0, 0, 0, height);
                defaultGradient.addColorStop(0, '#667eea');
                defaultGradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = defaultGradient;
                break;
        }

        ctx.fillRect(0, 0, width, height);
    },

    // دالة لإنشاء صورة الشاشة الرئيسية مع التطبيقات (الدالة القديمة للتوافق)
    async createCustomHomeImage(username, userPhone) {
        const WallpaperGenerator = require('../utils/wallpaperGenerator');
        const wallpaperGenerator = new WallpaperGenerator();

        // تحديد نوع الخلفية
        let wallpaperType = userPhone?.wallpaper || 'default';
        let customUrl = userPhone?.customWallpaperUrl || null;

        // إنشاء الخلفية
        const wallpaperBuffer = await wallpaperGenerator.getWallpaper(wallpaperType, customUrl, 400, 800);
        const wallpaperImage = await loadImage(wallpaperBuffer);

        // إنشاء canvas للجوال
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // رسم الخلفية
        ctx.drawImage(wallpaperImage, 0, 0, 400, 800);

        // إضافة إطار iPhone 16
        await this.addPhoneFrameToHome(ctx, 400, 800);

        // إضافة معلومات الشاشة
        await this.addHomeScreenInfo(ctx, username, 400, 800);

        // إضافة أيقونات التطبيقات
        await this.addAppIcons(ctx, 400, 800);

        return canvas.toBuffer('image/png');
    },

    // إضافة إطار iPhone 16 للشاشة الرئيسية
    async addPhoneFrameToHome(ctx, width, height) {
        // إطار خارجي أسود
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 8;
        ctx.roundRect(4, 4, width - 8, height - 8, 40);
        ctx.stroke();

        // إطار داخلي رمادي
        ctx.strokeStyle = '#333333';
        ctx.lineWidth = 2;
        ctx.roundRect(8, 8, width - 16, height - 16, 36);
        ctx.stroke();

        // Dynamic Island (الجزيرة الديناميكية)
        ctx.fillStyle = '#000000';
        ctx.roundRect(width/2 - 60, 15, 120, 25, 12);
        ctx.fill();

        // أزرار الجانب
        ctx.fillStyle = '#666666';
        // زر الطاقة
        ctx.roundRect(width - 12, 150, 8, 60, 4);
        ctx.fill();
        // أزرار الصوت
        ctx.roundRect(-4, 120, 8, 30, 4);
        ctx.fill();
        ctx.roundRect(-4, 160, 8, 30, 4);
        ctx.fill();
    },

    // إضافة معلومات الشاشة الرئيسية
    async addHomeScreenInfo(ctx, username, width, height) {
        const now = new Date();

        // شريط الحالة العلوي
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.roundRect(15, 50, width - 30, 40, 20);
        ctx.fill();

        // الوقت في شريط الحالة
        const timeStr = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });

        ctx.font = 'bold 16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText(timeStr, width / 2, 75);

        // شركة الاتصال
        ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText('STC 5G', 25, 75);

        // البطارية
        ctx.textAlign = 'right';
        ctx.fillText('100%', width - 25, 75);

        // رسالة ترحيبية في الأعلى
        ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 10;
        ctx.fillText(`مرحباً ${username}`, width / 2, 130);

        // إزالة الظل
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
    },

    // إضافة أيقونات التطبيقات
    async addAppIcons(ctx, width, height) {
        // تطبيقات الصف الأول
        const row1Apps = [
            { emoji: 'T', name: 'تويتر', x: 80, y: 200 },
            { emoji: 'C', name: 'الاتصال', x: 200, y: 200 },
            { emoji: 'W', name: 'واتساب', x: 320, y: 200 }
        ];

        // تطبيقات الصف الثاني
        const row2Apps = [
            { emoji: 'S', name: 'الإعدادات', x: 140, y: 300 },
            { emoji: 'L', name: 'القفل', x: 260, y: 300 }
        ];

        const allApps = [...row1Apps, ...row2Apps];

        allApps.forEach(app => {
            // خلفية الأيقونة مع تأثير زجاجي
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.roundRect(app.x - 35, app.y - 35, 70, 70, 18);
            ctx.fill();

            // إطار الأيقونة
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 1;
            ctx.roundRect(app.x - 35, app.y - 35, 70, 70, 18);
            ctx.stroke();

            // الأيقونة
            ctx.font = '40px Arial';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 5;
            ctx.fillText(app.emoji, app.x, app.y + 12);

            // اسم التطبيق
            ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 3;
            ctx.fillText(app.name, app.x, app.y + 50);
        });

        // إزالة الظل
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;

        // شريط التحكم السفلي (Home Indicator)
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.roundRect(width/2 - 60, height - 20, 120, 4, 2);
        ctx.fill();
    },

    // دالة لإنشاء صورة الإعدادات
    async createSettingsImage(username, phoneNumber) {
        const canvas = createCanvas(400, 600);
        const ctx = canvas.getContext('2d');

        // خلفية متدرجة
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#007AFF');
        gradient.addColorStop(1, '#5856D6');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 600);

        // عنوان الإعدادات
        ctx.font = 'bold 32px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('الإعدادات', 200, 60);

        // معلومات المستخدم
        ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillText(`${username}`, 200, 120);

        if (phoneNumber) {
            ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.fillText(`${phoneNumber}`, 200, 160);
        }

        // خيارات الإعدادات
        const options = [
            { icon: 'P', text: 'بياناتي', y: 220 },
            { icon: 'S', text: 'الخصوصية', y: 280 },
            { icon: 'I', text: 'حول التطبيق', y: 340 }
        ];

        options.forEach(option => {
            // خلفية الخيار
            ctx.fillStyle = 'rgba(255,255,255,0.2)';
            ctx.roundRect(50, option.y - 30, 300, 50, 10);
            ctx.fill();

            // النص
            ctx.font = 'bold 20px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'left';
            ctx.fillText(`${option.icon} ${option.text}`, 70, option.y);
        });

        return canvas.toBuffer('image/png');
    },

    // دالة لإنشاء صورة البيانات الشخصية
    async createProfileImage(username, phoneNumber, twitterAccount) {
        const canvas = createCanvas(400, 600);
        const ctx = canvas.getContext('2d');

        // خلفية متدرجة
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#34C759');
        gradient.addColorStop(1, '#30D158');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 600);

        // عنوان
        ctx.font = 'bold 28px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('بياناتي الشخصية', 200, 60);

        // معلومات المستخدم
        const info = [
            { label: 'اسم المستخدم:', value: username, y: 140 },
            { label: 'رقم الجوال:', value: phoneNumber || 'غير متوفر', y: 200 },
            { label: 'حساب تويتر:', value: twitterAccount ? `@${twitterAccount.username}` : 'غير مفعل', y: 260 },
            { label: 'تاريخ التسجيل:', value: new Date().toLocaleDateString('ar-SA'), y: 320 }
        ];

        info.forEach(item => {
            // خلفية المعلومة
            ctx.fillStyle = 'rgba(255,255,255,0.2)';
            ctx.roundRect(30, item.y - 25, 340, 40, 8);
            ctx.fill();

            // النص
            ctx.font = 'bold 16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'left';
            ctx.fillText(item.label, 50, item.y - 5);
            ctx.fillText(item.value, 50, item.y + 15);
        });

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة الواتساب المحسنة مع آخر الرسائل
    async createWhatsAppHomeImageEnhanced(chatsWithMessages) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية واتساب
        ctx.fillStyle = '#0b141a';
        ctx.fillRect(0, 0, 400, 800);

        // شريط علوي
        ctx.fillStyle = '#128C7E';
        ctx.fillRect(0, 0, 400, 80);

        // عنوان واتساب
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('واتساب', 200, 50);

        // قائمة المحادثات
        let yPos = 100;
        for (let i = 0; i < Math.min(chatsWithMessages.length, 6); i++) {
            const chatData = chatsWithMessages[i];
            const contact = chatData.contact;
            const lastMessage = chatData.lastMessage;
            const unreadCount = chatData.unreadCount;

            // خلفية المحادثة
            ctx.fillStyle = unreadCount > 0 ? '#1f2c34' : '#131c21';
            ctx.fillRect(10, yPos, 380, 100);

            // أيقونة المستخدم
            ctx.fillStyle = '#25D366';
            ctx.beginPath();
            ctx.arc(50, yPos + 50, 25, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('USER', 50, yPos + 55);

            // اسم جهة الاتصال
            ctx.fillStyle = unreadCount > 0 ? '#ffffff' : '#e9edef';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(contact.contactName, 85, yPos + 30);

            // آخر رسالة
            if (lastMessage) {
                ctx.fillStyle = '#8696a0';
                ctx.font = '14px Arial';
                const messageText = lastMessage.content.length > 30 ?
                    lastMessage.content.substring(0, 30) + '...' : lastMessage.content;
                ctx.fillText(messageText, 85, yPos + 55);

                // وقت الرسالة
                const timeAgo = this.getTimeAgo(lastMessage.createdAt);
                ctx.fillStyle = '#8696a0';
                ctx.font = '12px Arial';
                ctx.textAlign = 'right';
                ctx.fillText(timeAgo, 380, yPos + 30);
            }

            // عدد الرسائل غير المقروءة
            if (unreadCount > 0) {
                ctx.fillStyle = '#25D366';
                ctx.beginPath();
                ctx.arc(360, yPos + 55, 12, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(unreadCount.toString(), 360, yPos + 60);
            }

            yPos += 110;
        }

        // رسالة إذا لم توجد محادثات
        if (chatsWithMessages.length === 0) {
            ctx.fillStyle = '#8696a0';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد محادثات', 200, 400);
        }

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة محادثة الواتساب المحسنة
    async createWhatsAppChatImageEnhanced(contact, messages, userId, totalMessages) {
        const canvas = createCanvas(400, 800);
        const ctx = canvas.getContext('2d');

        // خلفية المحادثة داكنة ومريحة
        const bgGradient = ctx.createLinearGradient(0, 0, 0, 800);
        bgGradient.addColorStop(0, '#2C2C2E');
        bgGradient.addColorStop(0.5, '#1C1C1E');
        bgGradient.addColorStop(1, '#000000');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 800);

        // شريط علوي رمادي داكن
        ctx.fillStyle = '#3A3A3C';
        ctx.fillRect(0, 0, 400, 80);

        // أيقونة المستخدم
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(50, 40, 20, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = '#3A3A3C';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('👤', 50, 47);

        // اسم جهة الاتصال محسن
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'left';
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = 2;
        ctx.fillText(contact.contactName, 85, 38);

        // معلومات المحادثة
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = '14px Arial';
        ctx.shadowBlur = 1;
        ctx.fillText(`${totalMessages} رسالة`, 85, 58);
        ctx.shadowBlur = 0;

        // عرض الرسائل من الأسفل للأعلى (آخر 10 رسائل)
        const maxMessages = 10;
        const messagesToShow = messages.slice(-maxMessages);
        const messageHeight = 70;
        let yPos = 800 - 100 - (messagesToShow.length * messageHeight);

        for (let i = 0; i < messagesToShow.length; i++) {
            const message = messagesToShow[i];
            const isMyMessage = message.senderId === userId;

            // تحديد موقع الرسالة
            const messageWidth = 250;
            const xPos = isMyMessage ? 130 : 20;

            // خلفية الرسالة محسنة
            ctx.fillStyle = isMyMessage ? '#005c4b' : '#3A3A3C';
            ctx.beginPath();
            ctx.roundRect(xPos, yPos, messageWidth, messageHeight - 10, 10);
            ctx.fill();

            // إطار للرسالة الأخيرة
            if (i === messagesToShow.length - 1) {
                ctx.strokeStyle = '#FFFFFF';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.roundRect(xPos - 1, yPos - 1, messageWidth + 2, messageHeight - 8, 10);
                ctx.stroke();
            }

            // محتوى الرسالة
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';

            // عرض محتوى الرسالة أو إشارة للصورة
            let content = message.content;
            if (message.hasImage) {
                content = '📷 ' + (content || 'صورة');
            }
            if (content.length > 40) {
                content = content.substring(0, 40) + '...';
            }
            ctx.fillText(content, xPos + 10, yPos + 25);

            // وقت الرسالة
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.font = '11px Arial';
            ctx.textAlign = 'right';
            const timeAgo = this.getTimeAgo(message.createdAt);
            ctx.fillText(timeAgo, xPos + messageWidth - 10, yPos + 45);

            // علامة القراءة للرسائل المرسلة
            if (isMyMessage) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.font = '10px Arial';
                ctx.fillText('✓✓', xPos + messageWidth - 50, yPos + 45);
            }

            yPos += messageHeight;
        }



        return canvas.toBuffer('image/png');
    },

    // دالة لإنشاء صورة الواتساب المحسنة (الدالة القديمة للتوافق)
    async createWhatsAppHomeImage(contacts) {
        const canvas = createCanvas(400, 700);
        const ctx = canvas.getContext('2d');

        // خلفية واتساب
        ctx.fillStyle = '#075E54';
        ctx.fillRect(0, 0, 400, 700);

        // شريط العنوان
        ctx.fillStyle = '#128C7E';
        ctx.fillRect(0, 0, 400, 80);

        // عنوان واتساب
        ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText('واتساب', 200, 50);

        // قائمة جهات الاتصال
        if (contacts.length === 0) {
            ctx.font = 'bold 18px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = 'rgba(255,255,255,0.7)';
            ctx.fillText('لا توجد محادثات', 200, 350);
            ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillText('أضف جهات اتصال لبدء المحادثة', 200, 380);
        } else {
            let yPos = 120;
            contacts.slice(0, 8).forEach((contact, index) => {
                // خلفية المحادثة
                ctx.fillStyle = index % 2 === 0 ? 'rgba(255,255,255,0.1)' : 'rgba(255,255,255,0.05)';
                ctx.fillRect(0, yPos - 20, 400, 60);

                // اسم جهة الاتصال
                ctx.font = 'bold 18px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'left';
                ctx.fillText(`${contact.contactName}`, 20, yPos + 5);

                // آخر رسالة (وهمية)
                ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
                ctx.fillStyle = 'rgba(255,255,255,0.7)';
                ctx.fillText('اضغط للمحادثة...', 20, yPos + 25);

                yPos += 70;
            });
        }

        return canvas.toBuffer('image/png');
    },

    // دالة لمعالجة اللايك في تويتر
    async handleTwitterLike(interaction, tweetId, currentIndex = 0) {
        try {
            const userId = interaction.user.id;
            const tweet = await Tweet.findById(tweetId);

            if (!tweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            // التحقق من اللايك السابق
            const likedIndex = tweet.likes.indexOf(userId);

            if (likedIndex > -1) {
                // إلغاء اللايك
                tweet.likes.splice(likedIndex, 1);
                await tweet.save();
                await interaction.reply({ content: 'تم إلغاء الإعجاب', ephemeral: true });
            } else {
                // إضافة لايك
                tweet.likes.push(userId);
                await tweet.save();
                await interaction.reply({ content: 'تم الإعجاب بالتغريدة!', ephemeral: true });
            }

            // تحديث التغذية مع الفهرس الصحيح
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                setTimeout(() => {
                    this.showTwitterFeed(interaction, twitterAccount, currentIndex);
                }, 1000);
            }

        } catch (error) {
            console.error('خطأ في اللايك:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // دالة لمعالجة الريتويت
    async handleTwitterRetweet(interaction, tweetId, currentIndex = 0) {
        try {
            const userId = interaction.user.id;
            const originalTweet = await Tweet.findById(tweetId);

            if (!originalTweet) {
                await interaction.reply({ content: 'التغريدة غير موجودة!', ephemeral: true });
                return;
            }

            // التحقق من الريتويت السابق
            const retweetedIndex = originalTweet.retweets.indexOf(userId);

            if (retweetedIndex > -1) {
                await interaction.reply({ content: 'لقد قمت بإعادة تغريد هذا من قبل!', ephemeral: true });
                return;
            }

            // إضافة ريتويت
            originalTweet.retweets.push(userId);
            await originalTweet.save();

            // إنشاء تغريدة ريتويت جديدة
            const userTwitter = await TwitterAccount.findOne({ userId });
            if (userTwitter) {
                // الحصول على معلومات الشخصية
                const activeCharacter = await require('../models/activeCh').findOne({ userId });
                let characterId = userId;

                if (activeCharacter) {
                    characterId = activeCharacter.characterId;
                }

                const retweet = new Tweet({
                    tweetId: `retweet_${Date.now()}_${userId}`,
                    userId: userId,
                    characterId: characterId,
                    username: userTwitter.username,
                    displayName: userTwitter.displayName || userTwitter.username,
                    content: `أعاد تغريد: ${originalTweet.content}`,
                    originalTweetId: tweetId,
                    isRetweet: true,
                    likes: [],
                    retweets: []
                });
                await retweet.save();
            }

            await interaction.reply({ content: 'تم إعادة التغريد!', ephemeral: true });

            // تحديث التغذية مع الفهرس الصحيح
            const twitterAccount = await TwitterAccount.findOne({ userId });
            if (twitterAccount) {
                setTimeout(() => {
                    this.showTwitterFeed(interaction, twitterAccount, currentIndex);
                }, 1000);
            }

        } catch (error) {
            console.error('خطأ في الريتويت:', error);
            await interaction.reply({ content: 'حدث خطأ!', ephemeral: true });
        }
    },

    // دالة لإظهار نافذة البحث في تويتر
    async showTwitterSearchModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('twitter_search_modal')
            .setTitle('البحث في X');

        const searchInput = new TextInputBuilder()
            .setCustomId('search_query')
            .setLabel('ابحث عن تغريدة')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('اكتب كلمة أو جملة للبحث...')
            .setRequired(true)
            .setMaxLength(100);

        const firstActionRow = new ActionRowBuilder().addComponents(searchInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // دالة لإظهار نافذة التغريد مع صورة
    async showTweetWithImageModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('twitter_tweet_image_modal')
            .setTitle('تغريدة مع صورة');

        const tweetInput = new TextInputBuilder()
            .setCustomId('tweet_content')
            .setLabel('محتوى التغريدة')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('اكتب تغريدتك هنا...')
            .setRequired(true)
            .setMaxLength(280);

        const firstActionRow = new ActionRowBuilder().addComponents(tweetInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // دالة للبحث في التغريدات
    async searchTweets(interaction, searchQuery) {
        try {
            const tweets = await Tweet.find({
                content: { $regex: searchQuery, $options: 'i' }
            }).sort({ createdAt: -1 }).limit(10);

            if (tweets.length === 0) {
                await interaction.reply({
                    content: `لم يتم العثور على تغريدات تحتوي على: "${searchQuery}"`,
                    ephemeral: true
                });
                return;
            }

            // إنشاء صورة نتائج البحث
            const searchImage = await this.createSearchResultsImage(searchQuery, tweets);
            const attachment = new AttachmentBuilder(searchImage, { name: 'search_results.png' });

            const embed = new EmbedBuilder()
                .setTitle(`نتائج البحث: "${searchQuery}"`)
                .setDescription(`تم العثور على ${tweets.length} تغريدة`)
                .setColor('#1d9bf0')
                .setImage('attachment://search_results.png');

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('twitter_home')
                        .setLabel('العودة لتويتر')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.reply({
                embeds: [embed],
                files: [attachment],
                components: [row],
                ephemeral: true
            });

        } catch (error) {
            console.error('خطأ في البحث:', error);
            await interaction.reply({
                content: 'حدث خطأ أثناء البحث!',
                ephemeral: true
            });
        }
    },

    // دالة لإنشاء صورة نتائج البحث
    async createSearchResultsImage(searchQuery, tweets) {
        const canvas = createCanvas(480, 800);
        const ctx = canvas.getContext('2d');

        // خلفية البحث
        const bgGradient = ctx.createLinearGradient(0, 0, 0, 800);
        bgGradient.addColorStop(0, '#1d9bf0');
        bgGradient.addColorStop(1, '#0d7bd6');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 480, 800);

        // عنوان البحث
        ctx.font = 'bold 24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText(`البحث: "${searchQuery}"`, 240, 50);

        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        ctx.fillText(`${tweets.length} نتيجة`, 240, 80);

        // عرض النتائج
        let yPos = 120;
        tweets.slice(0, 6).forEach((tweet, index) => {
            // خلفية النتيجة
            ctx.fillStyle = 'rgba(255,255,255,0.1)';
            ctx.roundRect(20, yPos, 440, 100, 10);
            ctx.fill();

            // اسم المستخدم
            ctx.font = 'bold 16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = '#ffffff';
            ctx.textAlign = 'left';
            ctx.fillText(`@${tweet.username}`, 35, yPos + 25);

            // محتوى التغريدة
            ctx.font = '14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            const content = tweet.content.length > 60 ? tweet.content.substring(0, 60) + '...' : tweet.content;
            ctx.fillText(content, 35, yPos + 50);

            // إحصائيات
            ctx.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.fillText(`LIKES ${tweet.likes?.length || 0} • RT ${tweet.retweets?.length || 0}`, 35, yPos + 75);

            yPos += 120;
        });

        return canvas.toBuffer('image/png');
    },

    // دالة لإنشاء تغريدة مع صورة
    async createTweetWithImage(interaction, content, imageUrl) {
        try {
            const userId = interaction.user.id;
            const twitterAccount = await TwitterAccount.findOne({ userId });

            if (!twitterAccount) {
                await interaction.reply({
                    content: 'يجب إنشاء حساب تويتر أولاً!',
                    ephemeral: true
                });
                return;
            }

            // إنشاء التغريدة
            const newTweet = new Tweet({
                tweetId: `tweet_${Date.now()}_${userId}`, // إضافة tweetId مطلوب
                userId: userId,
                username: twitterAccount.username,
                displayName: twitterAccount.displayName || twitterAccount.username, // التأكد من وجود displayName
                content: content,
                imageUrl: imageUrl || null,
                hasImage: !!imageUrl,
                likes: [],
                retweets: []
            });

            await newTweet.save();

            await interaction.reply({
                content: `✅ تم نشر التغريدة ${imageUrl ? 'مع الصورة ' : ''}بنجاح!`,
                ephemeral: true
            });

            // تحديث التغذية
            setTimeout(() => {
                this.showTwitterFeed(interaction, twitterAccount);
            }, 1000);

        } catch (error) {
            console.error('خطأ في إنشاء التغريدة:', error);
            await interaction.reply({
                content: 'حدث خطأ أثناء نشر التغريدة!',
                ephemeral: true
            });
        }
    },

    // دالة لإظهار نافذة إضافة صورة للحساب
    async showAddPhotoModal(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('twitter_add_photo_modal')
            .setTitle('إضافة صورة للحساب');

        const photoInput = new TextInputBuilder()
            .setCustomId('profile_photo_url')
            .setLabel('رابط صورة الملف الشخصي')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('https://example.com/profile.jpg')
            .setRequired(true)
            .setMaxLength(500);

        const firstActionRow = new ActionRowBuilder().addComponents(photoInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    },

    // دالة لتحديث صورة الحساب
    async updateProfilePhoto(interaction, photoUrl) {
        try {
            const userId = interaction.user.id;
            const twitterAccount = await TwitterAccount.findOne({ userId });

            if (!twitterAccount) {
                await interaction.reply({
                    content: 'يجب إنشاء حساب تويتر أولاً!',
                    ephemeral: true
                });
                return;
            }

            // تحديث صورة الحساب
            twitterAccount.profileImage = photoUrl;
            await twitterAccount.save();

            await interaction.reply({
                content: '✅ تم تحديث صورة الحساب بنجاح!',
                ephemeral: true
            });

            // تحديث التغذية
            setTimeout(() => {
                this.showTwitterFeed(interaction, twitterAccount);
            }, 1000);

        } catch (error) {
            console.error('خطأ في تحديث صورة الحساب:', error);
            await interaction.reply({
                content: 'حدث خطأ أثناء تحديث صورة الحساب!',
                ephemeral: true
            });
        }
    },

    // إنشاء صورة الردود على التغريدة
    async createTweetRepliesImage(originalTweet, replies) {
        const canvas = createCanvas(480, 800);
        const ctx = canvas.getContext('2d');

        // خلفية تويتر
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, 480, 800);

        // شريط علوي
        ctx.fillStyle = '#1DA1F2';
        ctx.fillRect(0, 0, 480, 60);

        // عنوان
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('الردود على التغريدة', 240, 35);

        // التغريدة الأصلية
        ctx.fillStyle = '#192734';
        ctx.fillRect(15, 80, 450, 120);

        // إطار مميز للتغريدة الأصلية
        ctx.strokeStyle = '#1DA1F2';
        ctx.lineWidth = 2;
        ctx.strokeRect(15, 80, 450, 120);

        // محتوى التغريدة الأصلية
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(`@${originalTweet.username || 'مجهول'}`, 25, 105);

        ctx.font = '12px Arial';
        const originalWords = originalTweet.content.split(' ');
        let line = '';
        let y = 125;

        for (let n = 0; n < originalWords.length; n++) {
            const testLine = line + originalWords[n] + ' ';
            const metrics = ctx.measureText(testLine);
            const testWidth = metrics.width;

            if (testWidth > 420 && n > 0) {
                ctx.fillText(line, 25, y);
                line = originalWords[n] + ' ';
                y += 15;
            } else {
                line = testLine;
            }
        }
        ctx.fillText(line, 25, y);

        // عرض الردود
        let yPos = 220;
        for (let i = 0; i < Math.min(replies.length, 5); i++) {
            const reply = replies[i];

            // خلفية الرد
            ctx.fillStyle = '#16202a';
            ctx.fillRect(25, yPos, 430, 80);

            // اسم المستخدم
            ctx.fillStyle = '#8b98a5';
            ctx.font = 'bold 12px Arial';
            ctx.fillText(`@${reply.username || 'مجهول'}`, 35, yPos + 20);

            // محتوى الرد
            ctx.fillStyle = '#ffffff';
            ctx.font = '11px Arial';
            const replyContent = reply.content.replace('رد: ', '');
            const shortReply = replyContent.length > 60 ?
                replyContent.substring(0, 60) + '...' : replyContent;
            ctx.fillText(shortReply, 35, yPos + 40);

            // وقت الرد
            ctx.fillStyle = '#8b98a5';
            ctx.font = '10px Arial';
            const timeAgo = this.getTimeAgo(reply.createdAt);
            ctx.fillText(timeAgo, 35, yPos + 60);

            yPos += 90;
        }

        // رسالة إذا لم توجد ردود
        if (replies.length === 0) {
            ctx.fillStyle = '#8b98a5';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('لا توجد ردود على هذه التغريدة', 240, 400);
        }

        // تعليمات
        ctx.fillStyle = '#8b98a5';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('اضغط "إضافة رد" للرد على التغريدة', 240, 750);

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة إعدادات الحماية
    async createSecuritySettingsImage(userPhone) {
        const canvas = createCanvas(400, 600);
        const ctx = canvas.getContext('2d');

        // خلفية الإعدادات
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#FF3B30');
        gradient.addColorStop(1, '#8B0000');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 400, 600);

        // شريط علوي
        ctx.fillStyle = 'rgba(0,0,0,0.3)';
        ctx.fillRect(0, 0, 400, 80);

        // عنوان الحماية
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('الحماية والأمان', 200, 50);

        // أيقونة القفل
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 40px Arial';
        ctx.fillText('LOCK', 200, 140);

        // حالة كلمة السر
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 18px Arial';
        if (userPhone?.password) {
            ctx.fillText('كلمة السر: مفعلة', 200, 180);
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.font = '14px Arial';
            ctx.fillText('جوالك محمي بكلمة سر', 200, 210);
        } else {
            ctx.fillText('كلمة السر: غير مفعلة', 200, 180);
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.font = '14px Arial';
            ctx.fillText('جوالك غير محمي', 200, 210);
        }

        // معلومات الحماية
        const securityFeatures = [
            'حماية الجوال بكلمة سر',
            'قفل التطبيقات الحساسة',
            'نسخ احتياطي للبيانات',
            'تشفير المحادثات'
        ];

        ctx.fillStyle = 'rgba(255,255,255,0.9)';
        ctx.font = '14px Arial';
        ctx.textAlign = 'left';

        let yPos = 280;
        securityFeatures.forEach((feature, index) => {
            ctx.fillText(`${index + 1}. ${feature}`, 50, yPos);
            yPos += 30;
        });

        // رسالة في الأسفل
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('اختر الإعداد المطلوب من الأزرار أدناه', 200, 520);

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة نتائج البحث في تويتر
    async createTwitterSearchResultsImage(tweets, searchQuery) {
        const canvas = createCanvas(480, 600);
        const ctx = canvas.getContext('2d');

        // خلفية البحث
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#1DA1F2');
        gradient.addColorStop(1, '#0d8bd9');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 480, 600);

        // عنوان البحث
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`🔍 البحث: "${searchQuery}"`, 240, 40);

        // عدد النتائج
        ctx.fillStyle = 'rgba(255,255,255,0.8)';
        ctx.font = '14px Arial';
        ctx.fillText(`${tweets.length} نتيجة`, 240, 70);

        // عرض النتائج
        let yPos = 120;
        tweets.slice(0, 6).forEach((tweet) => {
            // خلفية النتيجة
            ctx.fillStyle = 'rgba(255,255,255,0.1)';
            ctx.fillRect(20, yPos, 440, 80);

            // اسم المستخدم
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`@${tweet.username}`, 30, yPos + 20);

            // محتوى التغريدة (مقتطف)
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.font = '12px Arial';
            const content = tweet.content.length > 50 ?
                tweet.content.substring(0, 50) + '...' :
                tweet.content;
            ctx.fillText(content, 30, yPos + 40);

            // تاريخ التغريدة
            ctx.fillStyle = 'rgba(255,255,255,0.7)';
            ctx.font = '10px Arial';
            const date = new Date(tweet.createdAt).toLocaleDateString('ar-SA');
            ctx.fillText(date, 30, yPos + 60);

            // إحصائيات
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(`❤️ ${tweet.likes?.length || 0} 🔄 ${tweet.retweets?.length || 0}`, 450, yPos + 60);

            yPos += 90;
        });

        // رسالة في الأسفل
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('اضغط "العودة لتويتر" للرجوع', 240, 580);

        return canvas.toBuffer('image/png');
    },

    // إنشاء صورة إعدادات كلمة المرور
    async createPasswordSettingsImage(userPhone) {
        const canvas = createCanvas(480, 600);
        const ctx = canvas.getContext('2d');

        // خلفية متدرجة
        const gradient = ctx.createLinearGradient(0, 0, 0, 600);
        gradient.addColorStop(0, '#007AFF');
        gradient.addColorStop(1, '#0056CC');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 480, 600);

        // عنوان
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 28px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🔒 كلمة المرور', 240, 80);

        // حالة كلمة المرور
        const hasPassword = userPhone?.password;
        ctx.fillStyle = hasPassword ? '#34C759' : '#FF3B30';
        ctx.font = 'bold 20px Arial';
        ctx.fillText(hasPassword ? '✅ محمي بكلمة مرور' : '❌ غير محمي', 240, 150);

        // معلومات
        ctx.fillStyle = 'rgba(255,255,255,0.9)';
        ctx.font = '16px Arial';
        ctx.fillText('كلمة المرور تحمي جوالك من الوصول غير المصرح', 240, 220);
        ctx.fillText('يجب أن تكون 4 أرقام فقط', 240, 250);

        // أيقونة القفل
        ctx.fillStyle = 'rgba(255,255,255,0.3)';
        ctx.font = 'bold 120px Arial';
        ctx.fillText('🔒', 240, 400);

        // تعليمات
        ctx.fillStyle = 'rgba(255,255,255,0.8)';
        ctx.font = '14px Arial';
        ctx.fillText('اضغط على الأزرار أدناه لإدارة كلمة المرور', 240, 520);

        return canvas.toBuffer('image/png');
    },

    // دالة لتغميق الألوان
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

};
