// سكريبت إصلاح فهارس قاعدة البيانات للجوالات
require('dotenv').config();
const mongoose = require('mongoose');

async function fixPhoneIndexes() {
    try {
        console.log('🔄 بدء إصلاح فهارس قاعدة البيانات...\n');

        // الاتصال بقاعدة البيانات
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ تم الاتصال بقاعدة البيانات');

        const db = mongoose.connection.db;
        const phonesCollection = db.collection('phones');

        // 1. عرض الفهارس الحالية
        console.log('\n📋 الفهارس الحالية:');
        const currentIndexes = await phonesCollection.indexes();
        currentIndexes.forEach((index, i) => {
            console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        });

        // 2. حذف الفهرس القديم userId_1 إذا كان موجوداً
        try {
            await phonesCollection.dropIndex('userId_1');
            console.log('\n✅ تم حذف الفهرس القديم userId_1');
        } catch (error) {
            if (error.code === 27) {
                console.log('\n⚠️  الفهرس userId_1 غير موجود (هذا طبيعي)');
            } else {
                console.log('\n❌ خطأ في حذف الفهرس:', error.message);
            }
        }

        // 3. حذف الفهرس القديم phoneNumber_1 إذا كان موجوداً
        try {
            await phonesCollection.dropIndex('phoneNumber_1');
            console.log('✅ تم حذف الفهرس القديم phoneNumber_1');
        } catch (error) {
            if (error.code === 27) {
                console.log('⚠️  الفهرس phoneNumber_1 غير موجود (هذا طبيعي)');
            } else {
                console.log('❌ خطأ في حذف الفهرس:', error.message);
            }
        }

        // 4. إنشاء الفهارس الجديدة
        console.log('\n🔨 إنشاء الفهارس الجديدة...');

        // فهرس فريد لكل شخصية
        try {
            await phonesCollection.createIndex(
                { userId: 1, characterId: 1 }, 
                { unique: true, name: 'userId_characterId_unique' }
            );
            console.log('✅ تم إنشاء فهرس userId + characterId (فريد)');
        } catch (error) {
            console.log('⚠️  فهرس userId + characterId موجود بالفعل');
        }

        // فهرس فريد لرقم الجوال
        try {
            await phonesCollection.createIndex(
                { phoneNumber: 1 }, 
                { unique: true, name: 'phoneNumber_unique' }
            );
            console.log('✅ تم إنشاء فهرس phoneNumber (فريد)');
        } catch (error) {
            console.log('⚠️  فهرس phoneNumber موجود بالفعل');
        }

        // فهارس للبحث السريع
        try {
            await phonesCollection.createIndex({ currentHolder: 1 });
            console.log('✅ تم إنشاء فهرس currentHolder');
        } catch (error) {
            console.log('⚠️  فهرس currentHolder موجود بالفعل');
        }

        try {
            await phonesCollection.createIndex({ originalOwner: 1 });
            console.log('✅ تم إنشاء فهرس originalOwner');
        } catch (error) {
            console.log('⚠️  فهرس originalOwner موجود بالفعل');
        }

        // 5. عرض الفهارس الجديدة
        console.log('\n📋 الفهارس بعد التحديث:');
        const newIndexes = await phonesCollection.indexes();
        newIndexes.forEach((index, i) => {
            console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        });

        // 6. التحقق من البيانات الموجودة
        console.log('\n📊 إحصائيات البيانات:');
        const totalPhones = await phonesCollection.countDocuments();
        console.log(`إجمالي الجوالات: ${totalPhones}`);

        const phonesWithoutCharacterId = await phonesCollection.countDocuments({
            $or: [
                { characterId: { $exists: false } },
                { characterId: null },
                { characterId: '' }
            ]
        });
        console.log(`جوالات بدون characterId: ${phonesWithoutCharacterId}`);

        if (phonesWithoutCharacterId > 0) {
            console.log('\n⚠️  يوجد جوالات بدون characterId. يُنصح بتشغيل migrate-phone-system.js');
        }

        console.log('\n🎉 تم إصلاح فهارس قاعدة البيانات بنجاح!');
        console.log('\n📝 الخطوات التالية:');
        console.log('1. إذا كان هناك جوالات بدون characterId، شغل: node migrate-phone-system.js');
        console.log('2. جرب فتح الجوال مرة أخرى');

    } catch (error) {
        console.error('❌ خطأ في إصلاح الفهارس:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
    }
}

// تشغيل السكريبت
if (require.main === module) {
    fixPhoneIndexes().catch(console.error);
}

module.exports = { fixPhoneIndexes };
