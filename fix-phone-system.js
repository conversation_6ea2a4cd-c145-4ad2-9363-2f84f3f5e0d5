// سكريبت شامل لإصلاح نظام الجوالات
require('dotenv').config();
const mongoose = require('mongoose');

async function fixCompletePhoneSystem() {
    try {
        console.log('🚀 بدء الإصلاح الشامل لنظام الجوالات...\n');

        // الاتصال بقاعدة البيانات
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ تم الاتصال بقاعدة البيانات\n');

        const db = mongoose.connection.db;
        const phonesCollection = db.collection('phones');

        // 1. إصلاح الفهارس
        console.log('🔧 المرحلة 1: إصلاح الفهارس...');
        
        // حذف الفهارس القديمة
        const indexesToDrop = ['userId_1', 'phoneNumber_1'];
        for (const indexName of indexesToDrop) {
            try {
                await phonesCollection.dropIndex(indexName);
                console.log(`✅ تم حذف الفهرس القديم: ${indexName}`);
            } catch (error) {
                if (error.code === 27) {
                    console.log(`⚠️  الفهرس ${indexName} غير موجود`);
                } else {
                    console.log(`❌ خطأ في حذف ${indexName}:`, error.message);
                }
            }
        }

        // إنشاء الفهارس الجديدة
        const newIndexes = [
            { key: { userId: 1, characterId: 1 }, options: { unique: true, name: 'userId_characterId_unique' } },
            { key: { phoneNumber: 1 }, options: { unique: true, name: 'phoneNumber_unique' } },
            { key: { currentHolder: 1 }, options: { name: 'currentHolder_1' } },
            { key: { originalOwner: 1 }, options: { name: 'originalOwner_1' } }
        ];

        for (const indexDef of newIndexes) {
            try {
                await phonesCollection.createIndex(indexDef.key, indexDef.options);
                console.log(`✅ تم إنشاء الفهرس: ${indexDef.options.name}`);
            } catch (error) {
                console.log(`⚠️  الفهرس ${indexDef.options.name} موجود بالفعل`);
            }
        }

        // 2. ترقية البيانات
        console.log('\n🔄 المرحلة 2: ترقية البيانات...');
        
        // استيراد النماذج
        const Phone = require('./models/Phone');
        const Contact = require('./models/Contact');
        const Call = require('./models/Call');
        const ActiveCharacter = require('./models/activeCh');

        // ترقية الجوالات
        const phonesWithoutCharacterId = await Phone.find({
            $or: [
                { characterId: { $exists: false } },
                { characterId: null },
                { characterId: '' }
            ]
        });

        let updatedPhones = 0;
        for (const phone of phonesWithoutCharacterId) {
            const activeChar = await ActiveCharacter.findOne({ userId: phone.userId });
            
            if (activeChar) {
                phone.characterId = activeChar.characterId;
            } else {
                phone.characterId = phone.userId; // استخدام userId كـ characterId افتراضي
            }
            
            try {
                await phone.save();
                updatedPhones++;
                console.log(`✅ تم تحديث جوال المستخدم ${phone.userId}`);
            } catch (error) {
                console.log(`❌ خطأ في تحديث جوال ${phone.userId}:`, error.message);
            }
        }

        // ترقية جهات الاتصال
        const contactsWithoutCharacterIds = await Contact.find({
            $or: [
                { ownerCharacterId: { $exists: false } },
                { contactCharacterId: { $exists: false } },
                { ownerCharacterId: null },
                { contactCharacterId: null }
            ]
        });

        let updatedContacts = 0;
        for (const contact of contactsWithoutCharacterIds) {
            const ownerActiveChar = await ActiveCharacter.findOne({ userId: contact.ownerId });
            contact.ownerCharacterId = ownerActiveChar ? ownerActiveChar.characterId : contact.ownerId;

            const contactActiveChar = await ActiveCharacter.findOne({ userId: contact.contactUserId });
            contact.contactCharacterId = contactActiveChar ? contactActiveChar.characterId : contact.contactUserId;

            try {
                await contact.save();
                updatedContacts++;
                console.log(`✅ تم تحديث جهة اتصال للمستخدم ${contact.ownerId}`);
            } catch (error) {
                console.log(`❌ خطأ في تحديث جهة اتصال ${contact.ownerId}:`, error.message);
            }
        }

        // ترقية المكالمات
        const callsWithoutCharacterIds = await Call.find({
            $or: [
                { callerCharacterId: { $exists: false } },
                { receiverCharacterId: { $exists: false } },
                { callerCharacterId: null },
                { receiverCharacterId: null }
            ]
        });

        let updatedCalls = 0;
        for (const call of callsWithoutCharacterIds) {
            const callerActiveChar = await ActiveCharacter.findOne({ userId: call.callerId });
            call.callerCharacterId = callerActiveChar ? callerActiveChar.characterId : call.callerId;

            const receiverActiveChar = await ActiveCharacter.findOne({ userId: call.receiverId });
            call.receiverCharacterId = receiverActiveChar ? receiverActiveChar.characterId : call.receiverId;

            try {
                await call.save();
                updatedCalls++;
                console.log(`✅ تم تحديث مكالمة بين ${call.callerId} و ${call.receiverId}`);
            } catch (error) {
                console.log(`❌ خطأ في تحديث مكالمة:`, error.message);
            }
        }

        // 3. التحقق النهائي
        console.log('\n📊 المرحلة 3: التحقق النهائي...');
        
        const finalStats = {
            totalPhones: await Phone.countDocuments(),
            phonesWithCharacterId: await Phone.countDocuments({ characterId: { $exists: true, $ne: null } }),
            totalContacts: await Contact.countDocuments(),
            contactsWithCharacterIds: await Contact.countDocuments({ 
                ownerCharacterId: { $exists: true, $ne: null },
                contactCharacterId: { $exists: true, $ne: null }
            }),
            totalCalls: await Call.countDocuments(),
            callsWithCharacterIds: await Call.countDocuments({
                callerCharacterId: { $exists: true, $ne: null },
                receiverCharacterId: { $exists: true, $ne: null }
            })
        };

        console.log('\n🎉 تم إكمال الإصلاح الشامل!');
        console.log('\n📈 إحصائيات النتائج:');
        console.log(`📱 الجوالات: ${finalStats.phonesWithCharacterId}/${finalStats.totalPhones} محدثة`);
        console.log(`📞 جهات الاتصال: ${finalStats.contactsWithCharacterIds}/${finalStats.totalContacts} محدثة`);
        console.log(`☎️  المكالمات: ${finalStats.callsWithCharacterIds}/${finalStats.totalCalls} محدثة`);
        console.log(`🔄 الجوالات المحدثة: ${updatedPhones}`);
        console.log(`📋 جهات الاتصال المحدثة: ${updatedContacts}`);
        console.log(`📞 المكالمات المحدثة: ${updatedCalls}`);

        if (finalStats.phonesWithCharacterId === finalStats.totalPhones) {
            console.log('\n✅ جميع البيانات تم ترقيتها بنجاح!');
            console.log('🎯 يمكنك الآن استخدام نظام الجوالات بدون مشاكل');
        } else {
            console.log('\n⚠️  بعض البيانات لم يتم ترقيتها. قد تحتاج لمراجعة يدوية.');
        }

    } catch (error) {
        console.error('❌ خطأ في الإصلاح الشامل:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
    }
}

// تشغيل السكريبت
if (require.main === module) {
    fixCompletePhoneSystem().catch(console.error);
}

module.exports = { fixCompletePhoneSystem };
