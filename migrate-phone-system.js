// سكريبت ترقية نظام الجوالات لدعم الشخصيات المنفصلة
const mongoose = require('mongoose');
const Phone = require('./models/Phone');
const Contact = require('./models/Contact');
const Call = require('./models/Call');
const ActiveCharacter = require('./models/activeCh');

async function migratePhoneSystem() {
    try {
        console.log('🔄 بدء ترقية نظام الجوالات...\n');

        // 1. ترقية الجوالات الموجودة
        console.log('📱 ترقية الجوالات الموجودة...');
        const phonesWithoutCharacterId = await Phone.find({ 
            $or: [
                { characterId: { $exists: false } },
                { characterId: null },
                { characterId: '' }
            ]
        });

        let updatedPhones = 0;
        for (const phone of phonesWithoutCharacterId) {
            // البحث عن الشخصية النشطة للمستخدم
            const activeChar = await ActiveCharacter.findOne({ userId: phone.userId });
            
            if (activeChar) {
                phone.characterId = activeChar.characterId;
                await phone.save();
                updatedPhones++;
                console.log(`✅ تم تحديث جوال المستخدم ${phone.userId} للشخصية ${activeChar.characterId}`);
            } else {
                // إذا لم توجد شخصية نشطة، استخدم userId كـ characterId افتراضي
                phone.characterId = phone.userId;
                await phone.save();
                updatedPhones++;
                console.log(`⚠️  تم تحديث جوال المستخدم ${phone.userId} بشخصية افتراضية`);
            }
        }
        console.log(`📱 تم تحديث ${updatedPhones} جوال\n`);

        // 2. ترقية جهات الاتصال الموجودة
        console.log('📞 ترقية جهات الاتصال الموجودة...');
        const contactsWithoutCharacterIds = await Contact.find({
            $or: [
                { ownerCharacterId: { $exists: false } },
                { contactCharacterId: { $exists: false } },
                { ownerCharacterId: null },
                { contactCharacterId: null }
            ]
        });

        let updatedContacts = 0;
        for (const contact of contactsWithoutCharacterIds) {
            // البحث عن الشخصية النشطة للمالك
            const ownerActiveChar = await ActiveCharacter.findOne({ userId: contact.ownerId });
            contact.ownerCharacterId = ownerActiveChar ? ownerActiveChar.characterId : contact.ownerId;

            // البحث عن الشخصية النشطة لجهة الاتصال
            const contactActiveChar = await ActiveCharacter.findOne({ userId: contact.contactUserId });
            contact.contactCharacterId = contactActiveChar ? contactActiveChar.characterId : contact.contactUserId;

            await contact.save();
            updatedContacts++;
            console.log(`✅ تم تحديث جهة اتصال للمستخدم ${contact.ownerId}`);
        }
        console.log(`📞 تم تحديث ${updatedContacts} جهة اتصال\n`);

        // 3. ترقية المكالمات الموجودة
        console.log('☎️  ترقية المكالمات الموجودة...');
        const callsWithoutCharacterIds = await Call.find({
            $or: [
                { callerCharacterId: { $exists: false } },
                { receiverCharacterId: { $exists: false } },
                { callerCharacterId: null },
                { receiverCharacterId: null }
            ]
        });

        let updatedCalls = 0;
        for (const call of callsWithoutCharacterIds) {
            // البحث عن الشخصية النشطة للمتصل
            const callerActiveChar = await ActiveCharacter.findOne({ userId: call.callerId });
            call.callerCharacterId = callerActiveChar ? callerActiveChar.characterId : call.callerId;

            // البحث عن الشخصية النشطة للمستقبل
            const receiverActiveChar = await ActiveCharacter.findOne({ userId: call.receiverId });
            call.receiverCharacterId = receiverActiveChar ? receiverActiveChar.characterId : call.receiverId;

            await call.save();
            updatedCalls++;
            console.log(`✅ تم تحديث مكالمة بين ${call.callerId} و ${call.receiverId}`);
        }
        console.log(`☎️  تم تحديث ${updatedCalls} مكالمة\n`);

        // 4. إنشاء الفهارس الجديدة
        console.log('🔍 إنشاء الفهارس الجديدة...');
        
        try {
            await Phone.collection.createIndex({ userId: 1, characterId: 1 }, { unique: true });
            console.log('✅ تم إنشاء فهرس Phone');
        } catch (error) {
            console.log('⚠️  فهرس Phone موجود بالفعل');
        }

        try {
            await Contact.collection.createIndex({ 
                ownerId: 1, 
                ownerCharacterId: 1, 
                contactUserId: 1, 
                contactCharacterId: 1 
            }, { unique: true });
            console.log('✅ تم إنشاء فهرس Contact');
        } catch (error) {
            console.log('⚠️  فهرس Contact موجود بالفعل');
        }

        try {
            await Call.collection.createIndex({ callerId: 1, callerCharacterId: 1 });
            await Call.collection.createIndex({ receiverId: 1, receiverCharacterId: 1 });
            console.log('✅ تم إنشاء فهارس Call');
        } catch (error) {
            console.log('⚠️  فهارس Call موجودة بالفعل');
        }

        // 5. تقرير نهائي
        console.log('\n🎉 تم إكمال الترقية بنجاح!');
        console.log('\n📊 إحصائيات الترقية:');
        console.log(`- الجوالات المحدثة: ${updatedPhones}`);
        console.log(`- جهات الاتصال المحدثة: ${updatedContacts}`);
        console.log(`- المكالمات المحدثة: ${updatedCalls}`);

        // 6. التحقق من النتائج
        console.log('\n🔍 التحقق من النتائج...');
        const totalPhones = await Phone.countDocuments();
        const phonesWithCharacterId = await Phone.countDocuments({ characterId: { $exists: true, $ne: null } });
        
        const totalContacts = await Contact.countDocuments();
        const contactsWithCharacterIds = await Contact.countDocuments({ 
            ownerCharacterId: { $exists: true, $ne: null },
            contactCharacterId: { $exists: true, $ne: null }
        });

        console.log(`📱 الجوالات: ${phonesWithCharacterId}/${totalPhones} لديها characterId`);
        console.log(`📞 جهات الاتصال: ${contactsWithCharacterIds}/${totalContacts} لديها characterIds`);

        if (phonesWithCharacterId === totalPhones && contactsWithCharacterIds === totalContacts) {
            console.log('\n✅ جميع البيانات تم ترقيتها بنجاح!');
        } else {
            console.log('\n⚠️  بعض البيانات لم يتم ترقيتها. يرجى المراجعة.');
        }

    } catch (error) {
        console.error('❌ خطأ في الترقية:', error);
        throw error;
    }
}

// تشغيل الترقية إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    console.log('⚠️  لتشغيل الترقية، تحتاج لتوصيل قاعدة البيانات أولاً');
    console.log('مثال:');
    console.log('mongoose.connect(process.env.MONGODB_URI).then(() => migratePhoneSystem());');
}

module.exports = { migratePhoneSystem };
